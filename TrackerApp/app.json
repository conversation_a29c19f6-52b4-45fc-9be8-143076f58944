{"expo": {"name": "TrackerApp", "slug": "TrackerApp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSFaceIDUsageDescription": "This app uses Face ID for secure authentication."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["USE_FINGERPRINT", "USE_BIOMETRIC"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-local-authentication", "expo-notifications"]}}