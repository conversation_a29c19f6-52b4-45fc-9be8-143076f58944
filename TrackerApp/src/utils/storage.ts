import AsyncStorage from '@react-native-async-storage/async-storage';
import { safeConsoleLog, safeConsoleError } from './errorUtils';

export const clearAllStorage = async () => {
  try {
    await AsyncStorage.clear();
    safeConsoleLog('Storage cleared successfully');
  } catch (error) {
    safeConsoleError('Error clearing storage:', error);
  }
};

export const clearUserData = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const userKeys = keys.filter(key =>
      key.includes('user') ||
      key.includes('expenses_') ||
      key.includes('workouts_') ||
      key.includes('water_') ||
      key.includes('weight_') ||
      key.includes('sleep_') ||
      key.includes('steps_') ||
      key.includes('budgets_')
    );
    await AsyncStorage.multiRemove(userKeys);
    safeConsoleLog('User data cleared successfully');
  } catch (error) {
    safeConsoleError('Error clearing user data:', error);
  }
};
