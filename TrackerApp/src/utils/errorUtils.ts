/**
 * Safe error logging utilities to prevent parsErrorStack.js issues
 */

export interface SafeError {
  message: string;
  stack?: string;
  name?: string;
  cause?: any;
}

/**
 * Safely converts any error to a serializable object
 */
export const safeErrorToObject = (error: any): SafeError => {
  if (!error) {
    return { message: 'Unknown error occurred' };
  }

  // If it's already a plain object, return it
  if (typeof error === 'object' && error.constructor === Object) {
    return {
      message: error.message || 'Unknown error',
      stack: error.stack || undefined,
      name: error.name || undefined,
      cause: error.cause || undefined,
    };
  }

  // If it's a string, wrap it
  if (typeof error === 'string') {
    return { message: error };
  }

  // If it's an Error object, safely extract properties
  if (error instanceof Error) {
    return {
      message: error.message || 'Unknown error',
      stack: error.stack || undefined,
      name: error.name || undefined,
      cause: (error as any).cause || undefined,
    };
  }

  // For any other type, try to stringify safely
  try {
    return {
      message: String(error),
      stack: undefined,
      name: typeof error,
    };
  } catch {
    return { message: 'Error could not be serialized' };
  }
};

/**
 * Safe console.error that won't cause parsErrorStack issues
 */
export const safeConsoleError = (message: string, error?: any, ...args: any[]) => {
  try {
    const safeError = error ? safeErrorToObject(error) : undefined;
    
    if (safeError) {
      console.error(message, {
        error: safeError,
        additionalArgs: args.length > 0 ? args : undefined,
      });
    } else {
      console.error(message, ...args);
    }
  } catch (consoleError) {
    // Fallback if even safe logging fails
    try {
      console.log(`[ERROR] ${message} - Console error occurred while logging`);
    } catch {
      // Silent fallback - don't let logging errors break the app
    }
  }
};

/**
 * Safe console.warn that won't cause parsErrorStack issues
 */
export const safeConsoleWarn = (message: string, error?: any, ...args: any[]) => {
  try {
    const safeError = error ? safeErrorToObject(error) : undefined;
    
    if (safeError) {
      console.warn(message, {
        error: safeError,
        additionalArgs: args.length > 0 ? args : undefined,
      });
    } else {
      console.warn(message, ...args);
    }
  } catch (consoleError) {
    // Fallback if even safe logging fails
    try {
      console.log(`[WARN] ${message} - Console warn occurred while logging`);
    } catch {
      // Silent fallback
    }
  }
};

/**
 * Safe console.log for debugging
 */
export const safeConsoleLog = (message: string, data?: any, ...args: any[]) => {
  try {
    if (data) {
      const safeData = typeof data === 'object' ? safeErrorToObject(data) : data;
      console.log(message, safeData, ...args);
    } else {
      console.log(message, ...args);
    }
  } catch {
    try {
      console.log(`[LOG] ${message} - Console log occurred while logging`);
    } catch {
      // Silent fallback
    }
  }
};

/**
 * Creates a safe error handler function
 */
export const createSafeErrorHandler = (context: string) => {
  return (error: any, additionalInfo?: any) => {
    safeConsoleError(`Error in ${context}:`, error, additionalInfo);
  };
};

/**
 * Wraps async functions with safe error handling
 */
export const withSafeErrorHandling = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context: string
): T => {
  return (async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      safeConsoleError(`Error in ${context}:`, error);
      throw error; // Re-throw to maintain original behavior
    }
  }) as T;
};

/**
 * Safe JSON stringify that won't throw on circular references
 */
export const safeStringify = (obj: any, space?: number): string => {
  try {
    const seen = new WeakSet();
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular Reference]';
        }
        seen.add(value);
      }
      return value;
    }, space);
  } catch {
    return '[Object could not be stringified]';
  }
};
