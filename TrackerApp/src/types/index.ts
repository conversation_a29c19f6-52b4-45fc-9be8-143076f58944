// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  weight?: number;
  height?: number;
  age?: number;
  fitnessGoal?: string;
  createdAt: string;
  updatedAt: string;
}

// Expense Types
export interface ExpenseCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
}

export interface Expense {
  id: string;
  userId: string;
  amount: number;
  description: string;
  category: ExpenseCategory;
  date: string;
  type: 'expense' | 'income';
  createdAt: string;
  updatedAt: string;
}

export interface Budget {
  id: string;
  userId: string;
  categoryId?: string;
  amount: number;
  period: 'monthly' | 'weekly' | 'yearly';
  startDate: string;
  endDate: string;
  spent: number;
}

export interface SalarySettings {
  salaryDay: number; // Day of the month (1-31)
  monthlySalary: number;
  isEnabled: boolean;
  userId: string;
}

// Fitness Types
export interface WorkoutExercise {
  id: string;
  name: string;
  duration: number; // in minutes
  caloriesBurned: number;
  sets?: number;
  reps?: number;
  weight?: number; // in kg
}

export interface Workout {
  id: string;
  userId: string;
  name: string;
  exercises: WorkoutExercise[];
  totalDuration: number;
  totalCalories: number;
  date: string;
  createdAt: string;
}

export interface WaterIntake {
  id: string;
  userId: string;
  amount: number; // in ml
  date: string;
  time: string;
}

export interface WeightEntry {
  id: string;
  userId: string;
  weight: number; // in kg
  date: string;
  bmi?: number;
}

export interface SleepEntry {
  id: string;
  userId: string;
  bedTime: string;
  wakeTime: string;
  duration: number; // in hours
  quality: 1 | 2 | 3 | 4 | 5; // 1-5 rating
  date: string;
}

export interface StepsData {
  id: string;
  userId: string;
  steps: number;
  distance: number; // in km
  calories: number;
  date: string;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  ExpenseForm: { expense?: Expense };
  WorkoutForm: { workout?: Workout };
  Profile: undefined;
  Settings: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Expenses: undefined;
  Fitness: undefined;
  Analytics: undefined;
};

export type ExpenseStackParamList = {
  ExpenseList: undefined;
  ExpenseForm: { expense?: Expense };
  ExpenseDetails: { expenseId: string };
  BudgetSettings: undefined;
};

export type FitnessStackParamList = {
  FitnessOverview: undefined;
  WorkoutForm: { workout?: Workout };
  WorkoutDetails: { workoutId: string };
  WaterTracker: undefined;
  WeightTracker: undefined;
  SleepTracker: undefined;
};

// Theme Types
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    error: string;
    success: string;
    warning: string;
    info: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    h1: {
      fontSize: number;
      fontWeight: string;
    };
    h2: {
      fontSize: number;
      fontWeight: string;
    };
    h3: {
      fontSize: number;
      fontWeight: string;
    };
    body: {
      fontSize: number;
      fontWeight: string;
    };
    caption: {
      fontSize: number;
      fontWeight: string;
    };
  };
  fonts: {
    regular: string;
    medium: string;
    bold: string;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
}

// Store Types
export interface RootState {
  auth: AuthState;
  expenses: ExpenseState;
  fitness: FitnessState;
  theme: ThemeState;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface ExpenseState {
  expenses: Expense[];
  categories: ExpenseCategory[];
  budgets: Budget[];
  salarySettings: SalarySettings | null;
  isLoading: boolean;
  error: string | null;
}

export interface FitnessState {
  workouts: Workout[];
  waterIntakes: WaterIntake[];
  weightEntries: WeightEntry[];
  sleepEntries: SleepEntry[];
  stepsData: StepsData[];
  isLoading: boolean;
  error: string | null;
}

export interface ThemeState {
  isDarkMode: boolean;
  theme: Theme;
}

// Form Types
export interface ExpenseFormData {
  amount: string;
  description: string;
  categoryId: string;
  date: string;
  type: 'expense' | 'income';
}

export interface WorkoutFormData {
  name: string;
  exercises: Omit<WorkoutExercise, 'id'>[];
  date: string;
}

export interface ProfileFormData {
  name: string;
  weight?: string;
  height?: string;
  age?: string;
  fitnessGoal?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Chart Data Types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface LineChartData {
  labels: string[];
  datasets: {
    data: number[];
    color?: string;
    strokeWidth?: number;
  }[];
}

// Notification Types
export interface NotificationData {
  id: string;
  title: string;
  body: string;
  type: 'reminder' | 'achievement' | 'warning' | 'info';
  scheduledTime?: string;
  isRead: boolean;
  createdAt: string;
}
