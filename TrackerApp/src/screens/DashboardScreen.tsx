import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from "react-native";
import styled from "styled-components/native";
import { useAppSelector, useAppDispatch } from "../store";
import { fetchExpenses, addExpense } from "../store/slices/expenseSlice";
import {
  fetchWorkouts,
  addWorkout,
  addWaterIntake,
  fetchWaterIntakes,
} from "../store/slices/fitnessSlice";
import { toggleTheme } from "../store/slices/themeSlice";
import { Card } from "../components/common/Card";
import { Button } from "../components/common/Button";
import Icon from "react-native-vector-icons/Ionicons";

const Container = styled(View)<{ theme: any }>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
`;

const Header = styled(View)<{ theme: any }>`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: ${(props) => props.theme.spacing.lg}px;
  padding-top: ${(props) => props.theme.spacing.xl}px;
`;

const WelcomeText = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h2.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h2.fontWeight};
  color: ${(props) => props.theme.colors.text};
`;

const UserName = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  margin-top: ${(props) => props.theme.spacing.xs}px;
`;

const ThemeButton = styled(TouchableOpacity)<{ theme: any }>`
  padding: ${(props) => props.theme.spacing.sm}px;
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  background-color: ${(props) => props.theme.colors.surface};
`;

const ContentContainer = styled(ScrollView)<{ theme: any }>`
  flex: 1;
  padding: 0 ${(props) => props.theme.spacing.lg}px;
`;

const SectionTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  margin-top: ${(props) => props.theme.spacing.lg}px;
`;

const StatsGrid = styled(View)<{ theme: any }>`
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const StatCard = styled(Card)<{ theme: any }>`
  flex: 1;
  min-width: 45%;
  margin: ${(props) => props.theme.spacing.sm}px;
`;

const StatHeader = styled(View)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  margin-bottom: ${(props) => props.theme.spacing.sm}px;
`;

const StatIcon = styled(View)<{ theme: any; color: string }>`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${(props) => props.color}20;
  align-items: center;
  justify-content: center;
  margin-right: ${(props) => props.theme.spacing.sm}px;
`;

const StatTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  flex: 1;
`;

const StatValue = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h2.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h2.fontWeight};
  color: ${(props) => props.theme.colors.text};
`;

const StatSubtext = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  margin-top: ${(props) => props.theme.spacing.xs}px;
`;

const QuickActionCard = styled(Card)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.md}px;
`;

const QuickActionHeader = styled(View)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  margin-bottom: ${(props) => props.theme.spacing.sm}px;
`;

const QuickActionTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-left: ${(props) => props.theme.spacing.sm}px;
`;

const QuickActionDescription = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
`;

const DashboardScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { expenses, isLoading: expensesLoading } = useAppSelector(
    (state) => state.expenses
  );
  const {
    workouts,
    waterIntakes,
    isLoading: fitnessLoading,
  } = useAppSelector((state) => state.fitness);
  const { isDarkMode, theme } = useAppSelector((state) => state.theme);

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [dispatch, user]);

  const loadData = async () => {
    if (user) {
      await Promise.all([
        dispatch(fetchExpenses(user.id)),
        dispatch(fetchWorkouts(user.id)),
        dispatch(fetchWaterIntakes(user.id)),
      ]);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const totalExpenses = expenses
    .filter((e) => e.type === "expense")
    .reduce((sum, expense) => sum + expense.amount, 0);

  const totalIncome = expenses
    .filter((e) => e.type === "income")
    .reduce((sum, expense) => sum + expense.amount, 0);

  const totalWorkouts = workouts.length;
  const totalCaloriesBurned = workouts.reduce(
    (sum, workout) => sum + workout.totalCalories,
    0
  );

  const handleThemeToggle = () => {
    dispatch(toggleTheme());
  };

  const handleQuickExpense = () => {
    Alert.prompt(
      "Quick Expense",
      "Enter expense amount:",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Add",
          onPress: async (amount) => {
            if (amount && user) {
              const expense = {
                userId: user.id,
                amount: parseFloat(amount) || 0,
                description: "Quick expense",
                category: {
                  id: "10",
                  name: "Other",
                  icon: "ellipsis-horizontal",
                  color: "#6B7280",
                },
                date: new Date().toISOString(),
                type: "expense" as const,
              };
              await dispatch(addExpense(expense));
              Alert.alert("Success", "Expense added successfully!");
            }
          },
        },
      ],
      "plain-text",
      "",
      "numeric"
    );
  };

  const handleQuickWorkout = () => {
    Alert.prompt(
      "Quick Workout",
      "Enter workout duration (minutes):",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Add",
          onPress: async (duration) => {
            if (duration && user) {
              const workout = {
                userId: user.id,
                name: "Quick Workout",
                exercises: [
                  {
                    name: "General Exercise",
                    duration: parseInt(duration) || 30,
                    caloriesBurned: (parseInt(duration) || 30) * 5, // 5 calories per minute
                  },
                ],
                totalDuration: parseInt(duration) || 30,
                totalCalories: (parseInt(duration) || 30) * 5,
                date: new Date().toISOString(),
              };
              await dispatch(addWorkout(workout));
              Alert.alert("Success", "Workout logged successfully!");
            }
          },
        },
      ],
      "plain-text",
      "",
      "numeric"
    );
  };

  const handleQuickWater = () => {
    Alert.alert("Water Intake", "Add water intake:", [
      { text: "Cancel", style: "cancel" },
      { text: "250ml", onPress: () => addWater(250) },
      { text: "500ml", onPress: () => addWater(500) },
      { text: "750ml", onPress: () => addWater(750) },
    ]);
  };

  const addWater = async (amount: number) => {
    if (user) {
      const waterIntake = {
        userId: user.id,
        amount,
        date: new Date().toISOString(),
        time: new Date().toISOString(),
      };
      await dispatch(addWaterIntake(waterIntake));
      Alert.alert("Success", `Added ${amount}ml of water!`);
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <View>
          <WelcomeText theme={theme}>Welcome back!</WelcomeText>
          <UserName theme={theme}>{user?.name}</UserName>
        </View>
        <ThemeButton theme={theme} onPress={handleThemeToggle}>
          <Icon
            name={isDarkMode ? "sunny" : "moon"}
            size={24}
            color={theme.colors.text}
          />
        </ThemeButton>
      </Header>

      <ContentContainer
        theme={theme}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <SectionTitle theme={theme}>Overview</SectionTitle>

        <StatsGrid theme={theme}>
          <StatCard theme={theme} shadow="small">
            <StatHeader theme={theme}>
              <StatIcon theme={theme} color={theme.colors.error}>
                <Icon
                  name="trending-down"
                  size={20}
                  color={theme.colors.error}
                />
              </StatIcon>
              <StatTitle theme={theme}>Expenses</StatTitle>
            </StatHeader>
            <StatValue theme={theme}>${totalExpenses.toFixed(2)}</StatValue>
            <StatSubtext theme={theme}>This month</StatSubtext>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatHeader theme={theme}>
              <StatIcon theme={theme} color={theme.colors.success}>
                <Icon
                  name="trending-up"
                  size={20}
                  color={theme.colors.success}
                />
              </StatIcon>
              <StatTitle theme={theme}>Income</StatTitle>
            </StatHeader>
            <StatValue theme={theme}>${totalIncome.toFixed(2)}</StatValue>
            <StatSubtext theme={theme}>This month</StatSubtext>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatHeader theme={theme}>
              <StatIcon theme={theme} color={theme.colors.primary}>
                <Icon name="fitness" size={20} color={theme.colors.primary} />
              </StatIcon>
              <StatTitle theme={theme}>Workouts</StatTitle>
            </StatHeader>
            <StatValue theme={theme}>{totalWorkouts}</StatValue>
            <StatSubtext theme={theme}>This month</StatSubtext>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatHeader theme={theme}>
              <StatIcon theme={theme} color={theme.colors.warning}>
                <Icon name="flame" size={20} color={theme.colors.warning} />
              </StatIcon>
              <StatTitle theme={theme}>Calories</StatTitle>
            </StatHeader>
            <StatValue theme={theme}>{totalCaloriesBurned}</StatValue>
            <StatSubtext theme={theme}>Burned this month</StatSubtext>
          </StatCard>
        </StatsGrid>

        <SectionTitle theme={theme}>Quick Actions</SectionTitle>

        <QuickActionCard
          theme={theme}
          shadow="small"
          onPress={handleQuickExpense}
        >
          <QuickActionHeader theme={theme}>
            <Icon name="add-circle" size={24} color={theme.colors.primary} />
            <QuickActionTitle theme={theme}>Add Expense</QuickActionTitle>
          </QuickActionHeader>
          <QuickActionDescription theme={theme}>
            Track your daily expenses and manage your budget
          </QuickActionDescription>
        </QuickActionCard>

        <QuickActionCard
          theme={theme}
          shadow="small"
          onPress={handleQuickWorkout}
        >
          <QuickActionHeader theme={theme}>
            <Icon name="barbell" size={24} color={theme.colors.secondary} />
            <QuickActionTitle theme={theme}>Log Workout</QuickActionTitle>
          </QuickActionHeader>
          <QuickActionDescription theme={theme}>
            Record your fitness activities and track progress
          </QuickActionDescription>
        </QuickActionCard>

        <QuickActionCard
          theme={theme}
          shadow="small"
          onPress={handleQuickWater}
        >
          <QuickActionHeader theme={theme}>
            <Icon name="water" size={24} color={theme.colors.info} />
            <QuickActionTitle theme={theme}>Water Intake</QuickActionTitle>
          </QuickActionHeader>
          <QuickActionDescription theme={theme}>
            Stay hydrated by tracking your daily water consumption
          </QuickActionDescription>
        </QuickActionCard>
      </ContentContainer>
    </Container>
  );
};

export default DashboardScreen;
