import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  ScrollView,
  Dimensions,
  Platform,
} from "react-native";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from "react-native-chart-kit";
import styled from "styled-components/native";
import { useAppSelector, useAppDispatch } from "../../store";
import {
  fetchExpenses,
  deleteExpense,
  addExpense,
  fetchSalarySettings,
  saveSalarySettings,
} from "../../store/slices/expenseSlice";
import { expenseCategories } from "../../constants/theme";
import { Card } from "../../components/common/Card";
import Icon from "react-native-vector-icons/Ionicons";
import { Expense } from "../../types";

const Container = styled(View)<{ theme: any }>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
`;

const ListContainer = styled(View)<{ theme: any }>`
  flex: 1;
  padding: ${(props) => props.theme.spacing.lg}px;
`;

const ExpenseItem = styled(Card)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  flex-direction: row;
  align-items: center;
`;

const CategoryIcon = styled(View)<{ theme: any; color: string }>`
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: ${(props) => props.color}20;
  align-items: center;
  justify-content: center;
  margin-right: ${(props) => props.theme.spacing.md}px;
`;

const ExpenseDetails = styled(View)<{ theme: any }>`
  flex: 1;
`;

const ExpenseDescription = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.xs}px;
`;

const ExpenseCategory = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
`;

const ExpenseAmount = styled(Text)<{ theme: any; type: string }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) =>
    props.type === "expense"
      ? props.theme.colors.error
      : props.theme.colors.success};
`;

const ActionButton = styled(TouchableOpacity)<{ theme: any }>`
  padding: ${(props) => props.theme.spacing.sm}px;
  margin-left: ${(props) => props.theme.spacing.sm}px;
`;

const EmptyState = styled(View)<{ theme: any }>`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: ${(props) => props.theme.spacing.xl}px;
`;

const EmptyText = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
  margin-top: ${(props) => props.theme.spacing.md}px;
`;

const FAB = styled(TouchableOpacity)<{ theme: any }>`
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: ${(props) => props.theme.colors.primary};
  align-items: center;
  justify-content: center;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.25;
  shadow-radius: 4px;
  elevation: 5;
`;

// Modal Components
const ModalContainer = styled(View)<{ theme: any }>`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: ${(props) => props.theme.spacing.lg}px;
`;

const ModalContent = styled(View)<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.borderRadius.lg}px;
  padding: ${(props) => props.theme.spacing.lg}px;
  width: 100%;
  max-width: 400px;
  max-height: 80%;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.3;
  shadow-radius: 8px;
  elevation: 8;
`;

const ModalTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  text-align: center;
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const ModalInput = styled(TextInput)<{ theme: any }>`
  border: 2px solid ${(props) => props.theme.colors.border};
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  padding: ${(props) => props.theme.spacing.md}px;
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  color: ${(props) => props.theme.colors.text};
  background-color: ${(props) => props.theme.colors.background};
  margin-bottom: ${(props) => props.theme.spacing.md}px;
`;

const ModalLabel = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.sm}px;
`;

const TypeSelector = styled(View)<{ theme: any }>`
  flex-direction: row;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  gap: ${(props) => props.theme.spacing.sm}px;
`;

const TypeButton = styled(TouchableOpacity)<{ theme: any; selected: boolean }>`
  flex: 1;
  padding: ${(props) => props.theme.spacing.md}px;
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  background-color: ${(props) =>
    props.selected ? props.theme.colors.primary : props.theme.colors.border};
  align-items: center;
`;

const TypeButtonText = styled(Text)<{ theme: any; selected: boolean }>`
  color: ${(props) => (props.selected ? "#FFFFFF" : props.theme.colors.text)};
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
`;

const ModalButtonContainer = styled(View)<{ theme: any }>`
  flex-direction: row;
  gap: ${(props) => props.theme.spacing.md}px;
  margin-top: ${(props) => props.theme.spacing.lg}px;
`;

const ModalButton = styled(TouchableOpacity)<{
  theme: any;
  variant: "primary" | "secondary";
}>`
  flex: 1;
  padding: ${(props) => props.theme.spacing.md}px;
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  background-color: ${(props) =>
    props.variant === "primary"
      ? props.theme.colors.primary
      : props.theme.colors.border};
  align-items: center;
`;

const ModalButtonText = styled(Text)<{
  theme: any;
  variant: "primary" | "secondary";
}>`
  color: ${(props) =>
    props.variant === "primary" ? "#FFFFFF" : props.theme.colors.text};
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
`;

// New styled components for enhanced features
const ChartSection = styled(View)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const ChartTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  text-align: center;
`;

const SalaryCard = styled(Card)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  padding: ${(props) => props.theme.spacing.lg}px;
  background-color: ${(props) => props.theme.colors.primary};
`;

const SalaryCardTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: #ffffff;
  margin-bottom: ${(props) => props.theme.spacing.sm}px;
  text-align: center;
`;

const SalaryCardContent = styled(View)<{ theme: any }>`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

const SalaryInfo = styled(View)<{ theme: any }>`
  flex: 1;
`;

const SalaryInfoText = styled(Text)<{ theme: any }>`
  color: #ffffff;
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  margin-bottom: ${(props) => props.theme.spacing.xs}px;
`;

const SalaryButton = styled(TouchableOpacity)<{ theme: any }>`
  background-color: rgba(255, 255, 255, 0.2);
  padding: ${(props) => props.theme.spacing.sm}px
    ${(props) => props.theme.spacing.md}px;
  border-radius: ${(props) => props.theme.borderRadius.md}px;
`;

const SalaryButtonText = styled(Text)<{ theme: any }>`
  color: #ffffff;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
`;

const ChartContainer = styled(View)<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.borderRadius.lg}px;
  padding: ${(props) => props.theme.spacing.md}px;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

// Category selection styled components
const CategorySelector = styled(View)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.md}px;
`;

const CategoryGrid = styled(View)<{ theme: any }>`
  flex-direction: row;
  flex-wrap: wrap;
  gap: ${(props) => props.theme.spacing.sm}px;
`;

const CategoryOption = styled(TouchableOpacity)<{
  theme: any;
  selected: boolean;
  color: string;
}>`
  flex-direction: row;
  align-items: center;
  padding: ${(props) => props.theme.spacing.sm}px
    ${(props) => props.theme.spacing.md}px;
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  border: 2px solid ${(props) => (props.selected ? props.color : "transparent")};
  background-color: ${(props) =>
    props.selected ? `${props.color}20` : props.theme.colors.surface};
  margin-bottom: ${(props) => props.theme.spacing.xs}px;
  min-width: 120px;
`;

const CategoryOptionIcon = styled(View)<{ theme: any; color: string }>`
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: ${(props) => `${props.color}20`};
  align-items: center;
  justify-content: center;
  margin-right: ${(props) => props.theme.spacing.sm}px;
`;

const CategoryOptionText = styled(Text)<{ theme: any; selected: boolean }>`
  color: ${(props) => props.theme.colors.text};
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) =>
    props.selected
      ? props.theme.typography.h3.fontWeight
      : props.theme.typography.body.fontWeight};
  flex: 1;
`;

const AddCategoryButton = styled(TouchableOpacity)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  padding: ${(props) => props.theme.spacing.sm}px
    ${(props) => props.theme.spacing.md}px;
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  border: 2px dashed ${(props) => props.theme.colors.primary};
  background-color: ${(props) => `${props.theme.colors.primary}10`};
  margin-bottom: ${(props) => props.theme.spacing.xs}px;
  min-width: 120px;
`;

const AddCategoryText = styled(Text)<{ theme: any }>`
  color: ${(props) => props.theme.colors.primary};
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  margin-left: ${(props) => props.theme.spacing.sm}px;
`;

// Chart period selector styled components
const ChartPeriodSelector = styled(View)<{ theme: any }>`
  flex-direction: row;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.borderRadius.lg}px;
  padding: ${(props) => props.theme.spacing.xs}px;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  border: 1px solid ${(props) => props.theme.colors.border};
`;

const ChartPeriodButton = styled(TouchableOpacity)<{
  theme: any;
  selected: boolean;
}>`
  flex: 1;
  padding: ${(props) => props.theme.spacing.sm}px
    ${(props) => props.theme.spacing.md}px;
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  background-color: ${(props) =>
    props.selected ? props.theme.colors.primary : "transparent"};
  align-items: center;
`;

const ChartPeriodButtonText = styled(Text)<{
  theme: any;
  selected: boolean;
}>`
  color: ${(props) => (props.selected ? "#FFFFFF" : props.theme.colors.text)};
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  font-weight: ${(props) =>
    props.selected
      ? props.theme.typography.h3.fontWeight
      : props.theme.typography.body.fontWeight};
`;

// Enhanced Summary Section styled components
const SummarySection = styled(View)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const SummaryHeader = styled(View)<{ theme: any }>`
  flex-direction: column;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  gap: ${(props) => props.theme.spacing.md}px;
`;

const SummaryTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
`;

const SummaryPeriodSelector = styled(View)<{ theme: any }>`
  flex-direction: row;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  padding: ${(props) => props.theme.spacing.xs}px;
  border: 1px solid ${(props) => props.theme.colors.border};
  flex-shrink: 0;
`;

const SummaryPeriodButton = styled(TouchableOpacity)<{
  theme: any;
  selected: boolean;
}>`
  flex: 1;
  padding: ${(props) => props.theme.spacing.sm}px
    ${(props) => props.theme.spacing.xs}px;
  border-radius: ${(props) => props.theme.borderRadius.sm}px;
  background-color: ${(props) =>
    props.selected ? props.theme.colors.primary : "transparent"};
  align-items: center;
  justify-content: center;
  min-width: 40px;
`;

const SummaryPeriodButtonText = styled(Text)<{
  theme: any;
  selected: boolean;
}>`
  color: ${(props) =>
    props.selected ? "#FFFFFF" : props.theme.colors.textSecondary};
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  font-weight: ${(props) =>
    props.selected
      ? props.theme.typography.h3.fontWeight
      : props.theme.typography.body.fontWeight};
`;

const EnhancedSummaryContainer = styled(View)<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.borderRadius.lg}px;
  padding: ${(props) => props.theme.spacing.lg}px;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

const SummaryGrid = styled(View)<{ theme: any }>`
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: ${(props) => props.theme.spacing.sm}px;
`;

const SummaryMetric = styled(View)<{ theme: any }>`
  width: 48%;
  min-width: 140px;
  align-items: center;
  padding: ${(props) => props.theme.spacing.md}px;
  background-color: ${(props) => props.theme.colors.background};
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  border: 1px solid ${(props) => props.theme.colors.border};
  margin-bottom: ${(props) => props.theme.spacing.sm}px;
`;

const SummaryMetricValue = styled(Text)<{ theme: any; color?: string }>`
  font-size: ${(props) => props.theme.typography.h2.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h2.fontWeight};
  color: ${(props) => props.color || props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.xs}px;
`;

const SummaryMetricLabel = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
`;

const SummaryMetricSubtext = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
  margin-top: ${(props) => props.theme.spacing.xs}px;
`;

// Web-compatible chart placeholder
const WebChartPlaceholder = styled(View)<{ theme: any }>`
  height: 220px;
  background-color: ${(props) => props.theme.colors.background};
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  border: 2px dashed ${(props) => props.theme.colors.border};
  justify-content: center;
  align-items: center;
  margin: ${(props) => props.theme.spacing.md}px 0;
`;

const WebChartPlaceholderText = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
  margin-top: ${(props) => props.theme.spacing.sm}px;
`;

// Enhanced Salary Setup styled components
const SalarySetupCard = styled(Card)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  padding: ${(props) => props.theme.spacing.xl}px;
  background-color: ${(props) => props.theme.colors.primary}15;
  border: 2px solid ${(props) => props.theme.colors.primary}30;
  border-radius: ${(props) => props.theme.borderRadius.xl}px;
`;

const SalarySetupHeader = styled(View)<{ theme: any }>`
  align-items: center;
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const SalarySetupIcon = styled(View)<{ theme: any }>`
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: ${(props) => props.theme.colors.primary};
  align-items: center;
  justify-content: center;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  shadow-color: ${(props) => props.theme.colors.primary};
  shadow-offset: 0px 4px;
  shadow-opacity: 0.3;
  shadow-radius: 8px;
  elevation: 8;
`;

const SalarySetupTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h2.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h1.fontWeight};
  color: ${(props) => props.theme.colors.text};
  text-align: center;
  margin-bottom: ${(props) => props.theme.spacing.sm}px;
`;

const SalarySetupSubtitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
  line-height: 22px;
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const SalarySetupFeatures = styled(View)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const SalarySetupFeature = styled(View)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
`;

const SalarySetupFeatureIcon = styled(View)<{ theme: any }>`
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: ${(props) => props.theme.colors.success}20;
  align-items: center;
  justify-content: center;
  margin-right: ${(props) => props.theme.spacing.md}px;
`;

const SalarySetupFeatureText = styled(Text)<{ theme: any }>`
  flex: 1;
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  color: ${(props) => props.theme.colors.text};
  line-height: 20px;
`;

const SalarySetupButton = styled(TouchableOpacity)<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.primary};
  padding: ${(props) => props.theme.spacing.lg}px
    ${(props) => props.theme.spacing.xl}px;
  border-radius: ${(props) => props.theme.borderRadius.xl}px;
  align-items: center;
  shadow-color: ${(props) => props.theme.colors.primary};
  shadow-offset: 0px 4px;
  shadow-opacity: 0.3;
  shadow-radius: 8px;
  elevation: 8;
`;

const SalarySetupButtonText = styled(Text)<{ theme: any }>`
  color: #ffffff;
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h2.fontWeight};
`;

const ExpenseListScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { expenses, salarySettings } = useAppSelector(
    (state) => state.expenses
  );
  const theme = useAppSelector((state) => state.theme.theme);

  const [refreshing, setRefreshing] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [expenseDescription, setExpenseDescription] = useState("");
  const [expenseAmount, setExpenseAmount] = useState("");
  const [expenseType, setExpenseType] = useState<"expense" | "income">(
    "expense"
  );
  const [salaryModalVisible, setSalaryModalVisible] = useState(false);
  const [salaryDay, setSalaryDay] = useState("");
  const [monthlySalary, setMonthlySalary] = useState("");
  const [selectedCategory, setSelectedCategory] = useState(
    expenseCategories[0]
  );
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState("");
  const [newCategoryIcon, setNewCategoryIcon] = useState("ellipsis-horizontal");
  const [customCategories, setCustomCategories] = useState<any[]>([]);
  const [chartPeriod, setChartPeriod] = useState<
    "7days" | "monthly" | "yearly"
  >("7days");
  const [summaryPeriod, setSummaryPeriod] = useState<
    "all" | "7days" | "monthly" | "yearly"
  >("monthly");

  useEffect(() => {
    if (user) {
      dispatch(fetchExpenses(user.id));
      dispatch(fetchSalarySettings(user.id));
    }
  }, [dispatch, user]);

  // Calculate days until next salary
  const getDaysUntilSalary = () => {
    if (!salarySettings?.isEnabled) return null;

    const today = new Date();
    const currentDay = today.getDate();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    let nextSalaryDate = new Date(
      currentYear,
      currentMonth,
      salarySettings.salaryDay
    );

    // If salary day has passed this month, move to next month
    if (currentDay >= salarySettings.salaryDay) {
      nextSalaryDate = new Date(
        currentYear,
        currentMonth + 1,
        salarySettings.salaryDay
      );
    }

    const timeDiff = nextSalaryDate.getTime() - today.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  };

  // Calculate average daily spending allowance
  const getDailySpendingAllowance = () => {
    if (!salarySettings?.isEnabled) return null;

    const daysUntilSalary = getDaysUntilSalary();
    if (!daysUntilSalary) return null;

    // Calculate total expenses this month
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthlyExpenses = expenses.filter((expense) => {
      const expenseDate = new Date(expense.date);
      return expense.type === "expense" && expenseDate >= startOfMonth;
    });

    const totalSpentThisMonth = monthlyExpenses.reduce(
      (sum, expense) => sum + expense.amount,
      0
    );
    const remainingBudget = salarySettings.monthlySalary - totalSpentThisMonth;

    return Math.max(0, remainingBudget / daysUntilSalary);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    if (user) {
      await dispatch(fetchExpenses(user.id));
    }
    setRefreshing(false);
  };

  const handleDeleteExpense = (expense: Expense) => {
    Alert.alert(
      "Delete Expense",
      `Are you sure you want to delete "${expense.description}"?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            if (user) {
              await dispatch(
                deleteExpense({ expenseId: expense.id, userId: user.id })
              );
              Alert.alert("Success", "Expense deleted successfully!");
            }
          },
        },
      ]
    );
  };

  const handleAddExpense = () => {
    setModalVisible(true);
  };

  const handleSaveExpense = async () => {
    if (!user) return;

    if (expenseDescription.trim() === "" || expenseAmount.trim() === "") {
      Alert.alert("Invalid Input", "Please enter both description and amount.");
      return;
    }

    const amount = parseFloat(expenseAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert(
        "Invalid Amount",
        "Please enter a valid amount greater than 0."
      );
      return;
    }

    try {
      const expense = {
        userId: user.id,
        amount: amount,
        description: expenseDescription.trim(),
        category: selectedCategory,
        date: new Date().toISOString(),
        type: expenseType,
      };

      await dispatch(addExpense(expense));

      // Close modal and reset form
      setModalVisible(false);
      setExpenseDescription("");
      setExpenseAmount("");
      setExpenseType("expense");
      setSelectedCategory(expenseCategories[0]);

      Alert.alert(
        "Success! 💰",
        `${
          expenseType === "expense" ? "Expense" : "Income"
        } added successfully!`,
        [{ text: "Great!", style: "default" }]
      );
    } catch (error) {
      console.error("Error adding expense:", error);
      Alert.alert("Error", "Failed to add expense. Please try again.");
    }
  };

  const handleCancelExpense = () => {
    setModalVisible(false);
    setExpenseDescription("");
    setExpenseAmount("");
    setExpenseType("expense");
    setSelectedCategory(expenseCategories[0]);
  };

  const handleSalarySettings = () => {
    if (salarySettings) {
      setSalaryDay(salarySettings.salaryDay.toString());
      setMonthlySalary(salarySettings.monthlySalary.toString());
    }
    setSalaryModalVisible(true);
  };

  const handleSaveSalarySettings = async () => {
    if (!user) return;

    const day = parseInt(salaryDay);
    const salary = parseFloat(monthlySalary);

    if (isNaN(day) || day < 1 || day > 31) {
      Alert.alert("Invalid Day", "Please enter a valid day between 1 and 31.");
      return;
    }

    if (isNaN(salary) || salary <= 0) {
      Alert.alert("Invalid Salary", "Please enter a valid salary amount.");
      return;
    }

    try {
      await dispatch(
        saveSalarySettings({
          salaryDay: day,
          monthlySalary: salary,
          isEnabled: true,
          userId: user.id,
        })
      );

      setSalaryModalVisible(false);
      setSalaryDay("");
      setMonthlySalary("");

      Alert.alert("Success! 💰", "Salary settings saved successfully!");
    } catch (error) {
      console.error("Error saving salary settings:", error);
      Alert.alert("Error", "Failed to save salary settings. Please try again.");
    }
  };

  const handleCancelSalarySettings = () => {
    setSalaryModalVisible(false);
    setSalaryDay("");
    setMonthlySalary("");
  };

  // Prepare chart data based on selected period
  const getChartData = () => {
    const today = new Date();

    if (chartPeriod === "7days") {
      // Get last 7 days of expenses
      const last7Days = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        last7Days.push(date);
      }

      const dailyExpenses = last7Days.map((date) => {
        const dayExpenses = expenses.filter((expense) => {
          const expenseDate = new Date(expense.date);
          return (
            expense.type === "expense" &&
            expenseDate.toDateString() === date.toDateString()
          );
        });
        return dayExpenses.reduce((sum, expense) => sum + expense.amount, 0);
      });

      return {
        labels: last7Days.map((date) => date.getDate().toString()),
        datasets: [
          {
            data: dailyExpenses,
            color: (opacity = 1) =>
              theme.colors.primary +
              Math.floor(opacity * 255)
                .toString(16)
                .padStart(2, "0"),
            strokeWidth: 2,
          },
        ],
      };
    } else if (chartPeriod === "monthly") {
      // Get last 12 months of expenses
      const last12Months = [];
      for (let i = 11; i >= 0; i--) {
        const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
        last12Months.push(date);
      }

      const monthlyExpenses = last12Months.map((date) => {
        const monthExpenses = expenses.filter((expense) => {
          const expenseDate = new Date(expense.date);
          return (
            expense.type === "expense" &&
            expenseDate.getMonth() === date.getMonth() &&
            expenseDate.getFullYear() === date.getFullYear()
          );
        });
        return monthExpenses.reduce((sum, expense) => sum + expense.amount, 0);
      });

      return {
        labels: last12Months.map((date) =>
          date.toLocaleDateString("en-US", { month: "short" })
        ),
        datasets: [
          {
            data: monthlyExpenses,
            color: (opacity = 1) =>
              theme.colors.secondary +
              Math.floor(opacity * 255)
                .toString(16)
                .padStart(2, "0"),
            strokeWidth: 2,
          },
        ],
      };
    } else {
      // Get last 5 years of expenses
      const last5Years = [];
      for (let i = 4; i >= 0; i--) {
        const year = today.getFullYear() - i;
        last5Years.push(year);
      }

      const yearlyExpenses = last5Years.map((year) => {
        const yearExpenses = expenses.filter((expense) => {
          const expenseDate = new Date(expense.date);
          return (
            expense.type === "expense" && expenseDate.getFullYear() === year
          );
        });
        return yearExpenses.reduce((sum, expense) => sum + expense.amount, 0);
      });

      return {
        labels: last5Years.map((year) => year.toString()),
        datasets: [
          {
            data: yearlyExpenses,
            color: (opacity = 1) =>
              theme.colors.success +
              Math.floor(opacity * 255)
                .toString(16)
                .padStart(2, "0"),
            strokeWidth: 2,
          },
        ],
      };
    }
  };

  const getPieChartData = () => {
    const today = new Date();
    let filteredExpenses = [];

    if (chartPeriod === "7days") {
      // Get last 7 days of expenses
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(today.getDate() - 6);

      filteredExpenses = expenses.filter((expense) => {
        const expenseDate = new Date(expense.date);
        return (
          expense.type === "expense" &&
          expenseDate >= sevenDaysAgo &&
          expenseDate <= today
        );
      });
    } else if (chartPeriod === "monthly") {
      // Get current month expenses
      const currentMonth = today.getMonth();
      const currentYear = today.getFullYear();

      filteredExpenses = expenses.filter((expense) => {
        const expenseDate = new Date(expense.date);
        return (
          expense.type === "expense" &&
          expenseDate.getMonth() === currentMonth &&
          expenseDate.getFullYear() === currentYear
        );
      });
    } else {
      // Get current year expenses
      const currentYear = today.getFullYear();

      filteredExpenses = expenses.filter((expense) => {
        const expenseDate = new Date(expense.date);
        return (
          expense.type === "expense" &&
          expenseDate.getFullYear() === currentYear
        );
      });
    }

    const categoryTotals = filteredExpenses.reduce((acc, expense) => {
      const categoryName = expense.category.name;
      const categoryColor = expense.category.color;
      if (!acc[categoryName]) {
        acc[categoryName] = { amount: 0, color: categoryColor };
      }
      acc[categoryName].amount += expense.amount;
      return acc;
    }, {} as Record<string, { amount: number; color: string }>);

    return Object.entries(categoryTotals).map(([name, data]) => ({
      name,
      amount: data.amount,
      color: data.color,
      legendFontColor: theme.colors.textSecondary,
      legendFontSize: 12,
    }));
  };

  // Enhanced calculation functions based on selected period
  const getFilteredExpenses = (
    period: "all" | "7days" | "monthly" | "yearly"
  ) => {
    const today = new Date();

    if (period === "all") {
      return expenses;
    } else if (period === "7days") {
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(today.getDate() - 6);
      return expenses.filter((expense) => {
        const expenseDate = new Date(expense.date);
        return expenseDate >= sevenDaysAgo && expenseDate <= today;
      });
    } else if (period === "monthly") {
      const currentMonth = today.getMonth();
      const currentYear = today.getFullYear();
      return expenses.filter((expense) => {
        const expenseDate = new Date(expense.date);
        return (
          expenseDate.getMonth() === currentMonth &&
          expenseDate.getFullYear() === currentYear
        );
      });
    } else {
      const currentYear = today.getFullYear();
      return expenses.filter((expense) => {
        const expenseDate = new Date(expense.date);
        return expenseDate.getFullYear() === currentYear;
      });
    }
  };

  const filteredExpenses = getFilteredExpenses(summaryPeriod);

  const totalExpenses = filteredExpenses
    .filter((e) => e.type === "expense")
    .reduce((sum, expense) => sum + expense.amount, 0);

  const totalIncome = filteredExpenses
    .filter((e) => e.type === "income")
    .reduce((sum, expense) => sum + expense.amount, 0);

  const balance = totalIncome - totalExpenses;

  // Additional metrics
  const getDayOfYear = (date: Date) => {
    const start = new Date(date.getFullYear(), 0, 0);
    const diff = date.getTime() - start.getTime();
    return Math.floor(diff / (1000 * 60 * 60 * 24));
  };

  const averageExpensePerDay =
    summaryPeriod === "7days"
      ? totalExpenses / 7
      : summaryPeriod === "monthly"
      ? totalExpenses / new Date().getDate()
      : summaryPeriod === "yearly"
      ? totalExpenses / getDayOfYear(new Date())
      : totalExpenses / (expenses.length || 1);

  const expenseCount = filteredExpenses.filter(
    (e) => e.type === "expense"
  ).length;
  const incomeCount = filteredExpenses.filter(
    (e) => e.type === "income"
  ).length;

  // Get period label
  const getPeriodLabel = () => {
    switch (summaryPeriod) {
      case "7days":
        return "Last 7 Days";
      case "monthly":
        return "This Month";
      case "yearly":
        return "This Year";
      default:
        return "All Time";
    }
  };

  const renderExpenseItem = ({ item }: { item: Expense }) => (
    <ExpenseItem theme={theme} shadow="small">
      <CategoryIcon theme={theme} color={item.category.color}>
        <Icon name={item.category.icon} size={24} color={item.category.color} />
      </CategoryIcon>
      <ExpenseDetails theme={theme}>
        <ExpenseDescription theme={theme}>
          {item.description}
        </ExpenseDescription>
        <ExpenseCategory theme={theme}>{item.category.name}</ExpenseCategory>
      </ExpenseDetails>
      <ExpenseAmount theme={theme} type={item.type}>
        {item.type === "expense" ? "-" : "+"}${item.amount.toFixed(2)}
      </ExpenseAmount>
      <ActionButton theme={theme} onPress={() => handleDeleteExpense(item)}>
        <Icon name="trash-outline" size={20} color={theme.colors.error} />
      </ActionButton>
    </ExpenseItem>
  );

  const daysUntilSalary = getDaysUntilSalary();
  const dailyAllowance = getDailySpendingAllowance();
  const chartData = getChartData();
  const pieChartData = getPieChartData();

  return (
    <Container theme={theme}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Salary Tracking Card */}
        {salarySettings?.isEnabled ? (
          <SalaryCard theme={theme}>
            <SalaryCardTitle theme={theme}>💰 Salary Tracker</SalaryCardTitle>
            <SalaryCardContent theme={theme}>
              <SalaryInfo theme={theme}>
                <SalaryInfoText theme={theme}>
                  Days until salary: {daysUntilSalary} days
                </SalaryInfoText>
                <SalaryInfoText theme={theme}>
                  Daily allowance: ${dailyAllowance?.toFixed(2) || "0.00"}
                </SalaryInfoText>
                <SalaryInfoText theme={theme}>
                  Next salary: {salarySettings.salaryDay}th of month
                </SalaryInfoText>
              </SalaryInfo>
              <SalaryButton theme={theme} onPress={handleSalarySettings}>
                <SalaryButtonText theme={theme}>Settings</SalaryButtonText>
              </SalaryButton>
            </SalaryCardContent>
          </SalaryCard>
        ) : (
          <SalarySetupCard theme={theme}>
            <SalarySetupHeader theme={theme}>
              <SalarySetupIcon theme={theme}>
                <Icon name="wallet" size={40} color="#FFFFFF" />
              </SalarySetupIcon>
              <SalarySetupTitle theme={theme}>
                Smart Salary Tracking
              </SalarySetupTitle>
              <SalarySetupSubtitle theme={theme}>
                Take control of your finances with intelligent spending insights
                and personalized recommendations
              </SalarySetupSubtitle>
            </SalarySetupHeader>

            <SalarySetupFeatures theme={theme}>
              <SalarySetupFeature theme={theme}>
                <SalarySetupFeatureIcon theme={theme}>
                  <Icon
                    name="calendar"
                    size={16}
                    color={theme.colors.success}
                  />
                </SalarySetupFeatureIcon>
                <SalarySetupFeatureText theme={theme}>
                  Track days until your next salary payment
                </SalarySetupFeatureText>
              </SalarySetupFeature>

              <SalarySetupFeature theme={theme}>
                <SalarySetupFeatureIcon theme={theme}>
                  <Icon
                    name="trending-up"
                    size={16}
                    color={theme.colors.success}
                  />
                </SalarySetupFeatureIcon>
                <SalarySetupFeatureText theme={theme}>
                  Get daily spending allowance recommendations
                </SalarySetupFeatureText>
              </SalarySetupFeature>

              <SalarySetupFeature theme={theme}>
                <SalarySetupFeatureIcon theme={theme}>
                  <Icon
                    name="analytics"
                    size={16}
                    color={theme.colors.success}
                  />
                </SalarySetupFeatureIcon>
                <SalarySetupFeatureText theme={theme}>
                  Monitor your budget and remaining balance
                </SalarySetupFeatureText>
              </SalarySetupFeature>

              <SalarySetupFeature theme={theme}>
                <SalarySetupFeatureIcon theme={theme}>
                  <Icon
                    name="shield-checkmark"
                    size={16}
                    color={theme.colors.success}
                  />
                </SalarySetupFeatureIcon>
                <SalarySetupFeatureText theme={theme}>
                  Stay on track with smart financial alerts
                </SalarySetupFeatureText>
              </SalarySetupFeature>
            </SalarySetupFeatures>

            <SalarySetupButton theme={theme} onPress={handleSalarySettings}>
              <SalarySetupButtonText theme={theme}>
                🚀 Start Tracking Now
              </SalarySetupButtonText>
            </SalarySetupButton>
          </SalarySetupCard>
        )}

        {/* Charts Section */}
        <ChartSection theme={theme}>
          {/* Chart Period Selector */}
          <ChartPeriodSelector theme={theme}>
            <ChartPeriodButton
              theme={theme}
              selected={chartPeriod === "7days"}
              onPress={() => setChartPeriod("7days")}
            >
              <ChartPeriodButtonText
                theme={theme}
                selected={chartPeriod === "7days"}
              >
                7 Days
              </ChartPeriodButtonText>
            </ChartPeriodButton>
            <ChartPeriodButton
              theme={theme}
              selected={chartPeriod === "monthly"}
              onPress={() => setChartPeriod("monthly")}
            >
              <ChartPeriodButtonText
                theme={theme}
                selected={chartPeriod === "monthly"}
              >
                Monthly
              </ChartPeriodButtonText>
            </ChartPeriodButton>
            <ChartPeriodButton
              theme={theme}
              selected={chartPeriod === "yearly"}
              onPress={() => setChartPeriod("yearly")}
            >
              <ChartPeriodButtonText
                theme={theme}
                selected={chartPeriod === "yearly"}
              >
                Yearly
              </ChartPeriodButtonText>
            </ChartPeriodButton>
          </ChartPeriodSelector>

          {/* Expenses Chart */}
          <ChartContainer theme={theme}>
            <ChartTitle theme={theme}>
              📊{" "}
              {chartPeriod === "7days"
                ? "Daily Expenses (Last 7 Days)"
                : chartPeriod === "monthly"
                ? "Monthly Expenses (Last 12 Months)"
                : "Yearly Expenses (Last 5 Years)"}
            </ChartTitle>
            {Platform.OS === "web" ? (
              <WebChartPlaceholder theme={theme}>
                <Icon
                  name="bar-chart-outline"
                  size={48}
                  color={theme.colors.textSecondary}
                />
                <WebChartPlaceholderText theme={theme}>
                  Charts are available on mobile devices.{"\n"}
                  Use the Expo Go app to view interactive charts.
                </WebChartPlaceholderText>
              </WebChartPlaceholder>
            ) : (
              <LineChart
                data={chartData}
                width={Dimensions.get("window").width - 40}
                height={220}
                chartConfig={{
                  backgroundColor: theme.colors.surface,
                  backgroundGradientFrom: theme.colors.surface,
                  backgroundGradientTo: theme.colors.surface,
                  decimalPlaces: 0,
                  color: (opacity = 1) =>
                    theme.colors.primary +
                    Math.floor(opacity * 255)
                      .toString(16)
                      .padStart(2, "0"),
                  labelColor: (opacity = 1) =>
                    theme.colors.text +
                    Math.floor(opacity * 255)
                      .toString(16)
                      .padStart(2, "0"),
                  style: {
                    borderRadius: 16,
                  },
                  propsForDots: {
                    r: "6",
                    strokeWidth: "2",
                    stroke: theme.colors.primary,
                  },
                  propsForBackgroundLines: {
                    strokeDasharray: "",
                    stroke: theme.colors.border,
                    strokeWidth: 1,
                  },
                }}
                bezier
                style={{
                  marginVertical: 8,
                  borderRadius: 16,
                }}
              />
            )}
          </ChartContainer>

          {/* Category Breakdown Chart */}
          {pieChartData.length > 0 && (
            <ChartContainer theme={theme}>
              <ChartTitle theme={theme}>
                🥧{" "}
                {chartPeriod === "7days"
                  ? "Weekly"
                  : chartPeriod === "monthly"
                  ? "Monthly"
                  : "Yearly"}{" "}
                Expenses by Category
              </ChartTitle>
              {Platform.OS === "web" ? (
                <WebChartPlaceholder theme={theme}>
                  <Icon
                    name="pie-chart-outline"
                    size={48}
                    color={theme.colors.textSecondary}
                  />
                  <WebChartPlaceholderText theme={theme}>
                    Category breakdown charts are available on mobile devices.
                    {"\n"}
                    Use the Expo Go app to view interactive pie charts.
                  </WebChartPlaceholderText>
                </WebChartPlaceholder>
              ) : (
                <PieChart
                  data={pieChartData}
                  width={Dimensions.get("window").width - 40}
                  height={220}
                  chartConfig={{
                    color: (opacity = 1) =>
                      theme.colors.text +
                      Math.floor(opacity * 255)
                        .toString(16)
                        .padStart(2, "0"),
                  }}
                  accessor="amount"
                  backgroundColor="transparent"
                  paddingLeft="15"
                  center={[0, 0]}
                  absolute
                  hasLegend={true}
                />
              )}
            </ChartContainer>
          )}
        </ChartSection>

        {/* Enhanced Summary Section */}
        <SummarySection theme={theme}>
          <SummaryHeader theme={theme}>
            <SummaryTitle theme={theme}>📊 Financial Summary</SummaryTitle>
            <SummaryPeriodSelector theme={theme}>
              <SummaryPeriodButton
                theme={theme}
                selected={summaryPeriod === "7days"}
                onPress={() => setSummaryPeriod("7days")}
              >
                <SummaryPeriodButtonText
                  theme={theme}
                  selected={summaryPeriod === "7days"}
                >
                  7D
                </SummaryPeriodButtonText>
              </SummaryPeriodButton>
              <SummaryPeriodButton
                theme={theme}
                selected={summaryPeriod === "monthly"}
                onPress={() => setSummaryPeriod("monthly")}
              >
                <SummaryPeriodButtonText
                  theme={theme}
                  selected={summaryPeriod === "monthly"}
                >
                  1M
                </SummaryPeriodButtonText>
              </SummaryPeriodButton>
              <SummaryPeriodButton
                theme={theme}
                selected={summaryPeriod === "yearly"}
                onPress={() => setSummaryPeriod("yearly")}
              >
                <SummaryPeriodButtonText
                  theme={theme}
                  selected={summaryPeriod === "yearly"}
                >
                  1Y
                </SummaryPeriodButtonText>
              </SummaryPeriodButton>
              <SummaryPeriodButton
                theme={theme}
                selected={summaryPeriod === "all"}
                onPress={() => setSummaryPeriod("all")}
              >
                <SummaryPeriodButtonText
                  theme={theme}
                  selected={summaryPeriod === "all"}
                >
                  All
                </SummaryPeriodButtonText>
              </SummaryPeriodButton>
            </SummaryPeriodSelector>
          </SummaryHeader>

          <EnhancedSummaryContainer theme={theme}>
            <SummaryGrid theme={theme}>
              <SummaryMetric theme={theme}>
                <SummaryMetricValue theme={theme} color={theme.colors.error}>
                  ${totalExpenses.toFixed(2)}
                </SummaryMetricValue>
                <SummaryMetricLabel theme={theme}>
                  Total Expenses
                </SummaryMetricLabel>
                <SummaryMetricSubtext theme={theme}>
                  {expenseCount} transactions
                </SummaryMetricSubtext>
              </SummaryMetric>

              <SummaryMetric theme={theme}>
                <SummaryMetricValue theme={theme} color={theme.colors.success}>
                  ${totalIncome.toFixed(2)}
                </SummaryMetricValue>
                <SummaryMetricLabel theme={theme}>
                  Total Income
                </SummaryMetricLabel>
                <SummaryMetricSubtext theme={theme}>
                  {incomeCount} transactions
                </SummaryMetricSubtext>
              </SummaryMetric>

              <SummaryMetric theme={theme}>
                <SummaryMetricValue
                  theme={theme}
                  color={
                    balance >= 0 ? theme.colors.success : theme.colors.error
                  }
                >
                  ${balance.toFixed(2)}
                </SummaryMetricValue>
                <SummaryMetricLabel theme={theme}>
                  Net Balance
                </SummaryMetricLabel>
                <SummaryMetricSubtext theme={theme}>
                  {getPeriodLabel()}
                </SummaryMetricSubtext>
              </SummaryMetric>

              <SummaryMetric theme={theme}>
                <SummaryMetricValue theme={theme} color={theme.colors.primary}>
                  ${averageExpensePerDay.toFixed(2)}
                </SummaryMetricValue>
                <SummaryMetricLabel theme={theme}>
                  Daily Average
                </SummaryMetricLabel>
                <SummaryMetricSubtext theme={theme}>
                  Expense per day
                </SummaryMetricSubtext>
              </SummaryMetric>

              {salarySettings?.isEnabled && (
                <SummaryMetric theme={theme}>
                  <SummaryMetricValue
                    theme={theme}
                    color={theme.colors.warning}
                  >
                    ${(salarySettings.monthlySalary - totalExpenses).toFixed(2)}
                  </SummaryMetricValue>
                  <SummaryMetricLabel theme={theme}>
                    Remaining Budget
                  </SummaryMetricLabel>
                  <SummaryMetricSubtext theme={theme}>
                    Until next salary
                  </SummaryMetricSubtext>
                </SummaryMetric>
              )}

              <SummaryMetric theme={theme}>
                <SummaryMetricValue theme={theme} color={theme.colors.info}>
                  {(
                    (totalIncome > 0 ? totalExpenses / totalIncome : 0) * 100
                  ).toFixed(1)}
                  %
                </SummaryMetricValue>
                <SummaryMetricLabel theme={theme}>
                  Expense Ratio
                </SummaryMetricLabel>
                <SummaryMetricSubtext theme={theme}>
                  Of total income
                </SummaryMetricSubtext>
              </SummaryMetric>
            </SummaryGrid>
          </EnhancedSummaryContainer>
        </SummarySection>

        <ListContainer theme={theme}>
          {expenses.length === 0 ? (
            <EmptyState theme={theme}>
              <Icon
                name="receipt-outline"
                size={80}
                color={theme.colors.textSecondary}
              />
              <EmptyText theme={theme}>
                No expenses yet.{"\n"}Tap the + button to add your first
                expense!
              </EmptyText>
            </EmptyState>
          ) : (
            <FlatList
              data={[...expenses].sort(
                (a, b) =>
                  new Date(b.date).getTime() - new Date(a.date).getTime()
              )}
              renderItem={renderExpenseItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
            />
          )}
        </ListContainer>
      </ScrollView>

      <FAB theme={theme} onPress={handleAddExpense}>
        <Icon name="add" size={30} color="#FFFFFF" />
      </FAB>

      {/* Add Expense Modal */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancelExpense}
      >
        <ModalContainer theme={theme}>
          <ModalContent theme={theme}>
            <ScrollView showsVerticalScrollIndicator={false}>
              <ModalTitle theme={theme}>💰 Add Transaction</ModalTitle>

              <ModalLabel theme={theme}>Type</ModalLabel>
              <TypeSelector theme={theme}>
                <TypeButton
                  theme={theme}
                  selected={expenseType === "expense"}
                  onPress={() => setExpenseType("expense")}
                >
                  <TypeButtonText
                    theme={theme}
                    selected={expenseType === "expense"}
                  >
                    Expense
                  </TypeButtonText>
                </TypeButton>
                <TypeButton
                  theme={theme}
                  selected={expenseType === "income"}
                  onPress={() => setExpenseType("income")}
                >
                  <TypeButtonText
                    theme={theme}
                    selected={expenseType === "income"}
                  >
                    Income
                  </TypeButtonText>
                </TypeButton>
              </TypeSelector>

              <ModalLabel theme={theme}>Category</ModalLabel>
              <CategorySelector theme={theme}>
                <CategoryGrid theme={theme}>
                  {[...expenseCategories, ...customCategories].map(
                    (category) => (
                      <CategoryOption
                        key={category.id}
                        theme={theme}
                        selected={selectedCategory.id === category.id}
                        color={category.color}
                        onPress={() => setSelectedCategory(category)}
                      >
                        <CategoryOptionIcon
                          theme={theme}
                          color={category.color}
                        >
                          <Icon
                            name={category.icon}
                            size={16}
                            color={category.color}
                          />
                        </CategoryOptionIcon>
                        <CategoryOptionText
                          theme={theme}
                          selected={selectedCategory.id === category.id}
                        >
                          {category.name}
                        </CategoryOptionText>
                      </CategoryOption>
                    )
                  )}
                  <AddCategoryButton
                    theme={theme}
                    onPress={() => setCategoryModalVisible(true)}
                  >
                    <Icon name="add" size={16} color={theme.colors.primary} />
                    <AddCategoryText theme={theme}>
                      Add Category
                    </AddCategoryText>
                  </AddCategoryButton>
                </CategoryGrid>
              </CategorySelector>

              <ModalLabel theme={theme}>Description</ModalLabel>
              <ModalInput
                theme={theme}
                value={expenseDescription}
                onChangeText={setExpenseDescription}
                placeholder="Enter description"
                placeholderTextColor={theme.colors.textSecondary}
                autoFocus={true}
              />

              <ModalLabel theme={theme}>Amount ($)</ModalLabel>
              <ModalInput
                theme={theme}
                value={expenseAmount}
                onChangeText={setExpenseAmount}
                placeholder="0.00"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
              />

              <ModalButtonContainer theme={theme}>
                <ModalButton
                  theme={theme}
                  variant="secondary"
                  onPress={handleCancelExpense}
                >
                  <ModalButtonText theme={theme} variant="secondary">
                    Cancel
                  </ModalButtonText>
                </ModalButton>
                <ModalButton
                  theme={theme}
                  variant="primary"
                  onPress={handleSaveExpense}
                >
                  <ModalButtonText theme={theme} variant="primary">
                    Save
                  </ModalButtonText>
                </ModalButton>
              </ModalButtonContainer>
            </ScrollView>
          </ModalContent>
        </ModalContainer>
      </Modal>

      {/* Salary Settings Modal */}
      <Modal
        visible={salaryModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancelSalarySettings}
      >
        <ModalContainer theme={theme}>
          <ModalContent theme={theme}>
            <ScrollView showsVerticalScrollIndicator={false}>
              <ModalTitle theme={theme}>💰 Salary Settings</ModalTitle>

              <ModalLabel theme={theme}>Salary Day (1-31)</ModalLabel>
              <ModalInput
                theme={theme}
                value={salaryDay}
                onChangeText={setSalaryDay}
                placeholder="e.g., 15"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
                autoFocus={true}
              />

              <ModalLabel theme={theme}>Monthly Salary ($)</ModalLabel>
              <ModalInput
                theme={theme}
                value={monthlySalary}
                onChangeText={setMonthlySalary}
                placeholder="e.g., 5000"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
              />

              <ModalButtonContainer theme={theme}>
                <ModalButton
                  theme={theme}
                  variant="secondary"
                  onPress={handleCancelSalarySettings}
                >
                  <ModalButtonText theme={theme} variant="secondary">
                    Cancel
                  </ModalButtonText>
                </ModalButton>
                <ModalButton
                  theme={theme}
                  variant="primary"
                  onPress={handleSaveSalarySettings}
                >
                  <ModalButtonText theme={theme} variant="primary">
                    Save Settings
                  </ModalButtonText>
                </ModalButton>
              </ModalButtonContainer>
            </ScrollView>
          </ModalContent>
        </ModalContainer>
      </Modal>

      {/* Add Category Modal */}
      <Modal
        visible={categoryModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setCategoryModalVisible(false)}
      >
        <ModalContainer theme={theme}>
          <ModalContent theme={theme}>
            <ScrollView showsVerticalScrollIndicator={false}>
              <ModalTitle theme={theme}>🏷️ Add Custom Category</ModalTitle>

              <ModalLabel theme={theme}>Category Name</ModalLabel>
              <ModalInput
                theme={theme}
                value={newCategoryName}
                onChangeText={setNewCategoryName}
                placeholder="e.g., Entertainment"
                placeholderTextColor={theme.colors.textSecondary}
                autoFocus={true}
              />

              <ModalLabel theme={theme}>Icon Name</ModalLabel>
              <ModalInput
                theme={theme}
                value={newCategoryIcon}
                onChangeText={setNewCategoryIcon}
                placeholder="e.g., game-controller"
                placeholderTextColor={theme.colors.textSecondary}
              />

              <ModalButtonContainer theme={theme}>
                <ModalButton
                  theme={theme}
                  variant="secondary"
                  onPress={() => {
                    setCategoryModalVisible(false);
                    setNewCategoryName("");
                    setNewCategoryIcon("ellipsis-horizontal");
                  }}
                >
                  <ModalButtonText theme={theme} variant="secondary">
                    Cancel
                  </ModalButtonText>
                </ModalButton>
                <ModalButton
                  theme={theme}
                  variant="primary"
                  onPress={() => {
                    if (newCategoryName.trim()) {
                      const newCategory = {
                        id: `custom_${Date.now()}`,
                        name: newCategoryName.trim(),
                        icon: newCategoryIcon || "ellipsis-horizontal",
                        color: "#6B7280", // Default gray color for custom categories
                      };
                      setCustomCategories((prev) => [...prev, newCategory]);
                      setSelectedCategory(newCategory);
                      setCategoryModalVisible(false);
                      setNewCategoryName("");
                      setNewCategoryIcon("ellipsis-horizontal");
                      Alert.alert(
                        "Success! 🏷️",
                        "Custom category added successfully!"
                      );
                    } else {
                      Alert.alert("Error", "Please enter a category name.");
                    }
                  }}
                >
                  <ModalButtonText theme={theme} variant="primary">
                    Add Category
                  </ModalButtonText>
                </ModalButton>
              </ModalButtonContainer>
            </ScrollView>
          </ModalContent>
        </ModalContainer>
      </Modal>
    </Container>
  );
};

export default ExpenseListScreen;
