import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  RefreshControl,
} from "react-native";
import styled from "styled-components/native";
import { useAppSelector, useAppDispatch } from "../store";
import { fetchExpenses } from "../store/slices/expenseSlice";
import {
  fetchWorkouts,
  fetchWaterIntakes,
  fetchWeightEntries,
} from "../store/slices/fitnessSlice";
import { Card } from "../components/common/Card";
import Icon from "react-native-vector-icons/Ionicons";

const { width } = Dimensions.get("window");

const Container = styled(View)<{ theme: any }>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
`;

const Header = styled(View)<{ theme: any }>`
  padding: ${(props) => props.theme.spacing.lg}px;
  background-color: ${(props) => props.theme.colors.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${(props) => props.theme.colors.border};
`;

const HeaderTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h2.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h2.fontWeight};
  color: ${(props) => props.theme.colors.text};
  text-align: center;
`;

const ContentContainer = styled(ScrollView)<{ theme: any }>`
  flex: 1;
  padding: ${(props) => props.theme.spacing.lg}px;
`;

const SectionTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  margin-top: ${(props) => props.theme.spacing.lg}px;
`;

const InsightCard = styled(Card)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.md}px;
`;

const InsightHeader = styled(View)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
`;

const InsightIcon = styled(View)<{ theme: any; color: string }>`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${(props) => props.color}20;
  align-items: center;
  justify-content: center;
  margin-right: ${(props) => props.theme.spacing.md}px;
`;

const InsightTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  flex: 1;
`;

const InsightValue = styled(Text)<{ theme: any; color?: string }>`
  font-size: ${(props) => props.theme.typography.h2.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h2.fontWeight};
  color: ${(props) => props.color || props.theme.colors.text};
  text-align: center;
  margin-bottom: ${(props) => props.theme.spacing.sm}px;
`;

const InsightDescription = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
  line-height: 20px;
`;

const ChartContainer = styled(View)<{ theme: any }>`
  height: 200px;
  margin: ${(props) => props.theme.spacing.md}px 0;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.borderRadius.lg}px;
  padding: ${(props) => props.theme.spacing.md}px;
  align-items: center;
  justify-content: center;
`;

const ChartPlaceholder = styled(View)<{ theme: any }>`
  align-items: center;
  justify-content: center;
  flex: 1;
`;

const ChartText = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
  margin-top: ${(props) => props.theme.spacing.sm}px;
`;

const StatsGrid = styled(View)<{ theme: any }>`
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const StatCard = styled(Card)<{ theme: any }>`
  flex: 1;
  min-width: 45%;
  margin: ${(props) => props.theme.spacing.sm}px;
  align-items: center;
`;

const StatValue = styled(Text)<{ theme: any; color?: string }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.color || props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.xs}px;
`;

const StatLabel = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
`;

const CategoryItem = styled(View)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: ${(props) => props.theme.spacing.sm}px 0;
  border-bottom-width: 1px;
  border-bottom-color: ${(props) => props.theme.colors.border};
`;

const CategoryInfo = styled(View)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  flex: 1;
`;

const CategoryIcon = styled(View)<{ theme: any; color: string }>`
  width: 30px;
  height: 30px;
  border-radius: 15px;
  background-color: ${(props) => props.color}20;
  align-items: center;
  justify-content: center;
  margin-right: ${(props) => props.theme.spacing.sm}px;
`;

const CategoryName = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  color: ${(props) => props.theme.colors.text};
  flex: 1;
`;

const CategoryAmount = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
`;

const AnalyticsScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { expenses } = useAppSelector((state) => state.expenses);
  const { workouts, waterIntakes, weightEntries } = useAppSelector(
    (state) => state.fitness
  );
  const theme = useAppSelector((state) => state.theme.theme);

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [dispatch, user]);

  const loadData = async () => {
    if (user) {
      await Promise.all([
        dispatch(fetchExpenses(user.id)),
        dispatch(fetchWorkouts(user.id)),
        dispatch(fetchWaterIntakes(user.id)),
        dispatch(fetchWeightEntries(user.id)),
      ]);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Calculate analytics data
  const totalExpenses = expenses
    .filter((e) => e.type === "expense")
    .reduce((sum, expense) => sum + expense.amount, 0);

  const totalIncome = expenses
    .filter((e) => e.type === "income")
    .reduce((sum, expense) => sum + expense.amount, 0);

  const balance = totalIncome - totalExpenses;

  // Expense by category
  const expensesByCategory = expenses
    .filter((e) => e.type === "expense")
    .reduce((acc, expense) => {
      const categoryName = expense.category.name;
      if (!acc[categoryName]) {
        acc[categoryName] = {
          amount: 0,
          category: expense.category,
        };
      }
      acc[categoryName].amount += expense.amount;
      return acc;
    }, {} as Record<string, { amount: number; category: any }>);

  const topCategories = Object.values(expensesByCategory)
    .sort((a, b) => b.amount - a.amount)
    .slice(0, 5);

  // Fitness analytics
  const totalWorkouts = workouts.length;
  const totalCalories = workouts.reduce((sum, w) => sum + w.totalCalories, 0);
  const avgCaloriesPerWorkout =
    totalWorkouts > 0 ? Math.round(totalCalories / totalWorkouts) : 0;

  const totalWater = waterIntakes.reduce((sum, w) => sum + w.amount, 0);
  const avgWaterPerDay =
    waterIntakes.length > 0 ? Math.round(totalWater / 7) : 0; // Assuming last 7 days

  const currentWeight =
    weightEntries.length > 0
      ? weightEntries[weightEntries.length - 1].weight
      : 0;
  const firstWeight = weightEntries.length > 0 ? weightEntries[0].weight : 0;
  const weightChange = currentWeight - firstWeight;

  // This month's data
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();

  const thisMonthExpenses = expenses
    .filter((e) => {
      const expenseDate = new Date(e.date);
      return (
        expenseDate.getMonth() === currentMonth &&
        expenseDate.getFullYear() === currentYear &&
        e.type === "expense"
      );
    })
    .reduce((sum, e) => sum + e.amount, 0);

  const thisMonthWorkouts = workouts.filter((w) => {
    const workoutDate = new Date(w.date);
    return (
      workoutDate.getMonth() === currentMonth &&
      workoutDate.getFullYear() === currentYear
    );
  }).length;

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <HeaderTitle theme={theme}>Analytics & Insights</HeaderTitle>
      </Header>

      <ContentContainer
        theme={theme}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <SectionTitle theme={theme}>Financial Overview</SectionTitle>

        <StatsGrid theme={theme}>
          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.error}>
              ${totalExpenses.toFixed(2)}
            </StatValue>
            <StatLabel theme={theme}>Total Expenses</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.success}>
              ${totalIncome.toFixed(2)}
            </StatValue>
            <StatLabel theme={theme}>Total Income</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue
              theme={theme}
              color={balance >= 0 ? theme.colors.success : theme.colors.error}
            >
              ${balance.toFixed(2)}
            </StatValue>
            <StatLabel theme={theme}>Net Balance</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.warning}>
              ${thisMonthExpenses.toFixed(2)}
            </StatValue>
            <StatLabel theme={theme}>This Month</StatLabel>
          </StatCard>
        </StatsGrid>

        <InsightCard theme={theme} shadow="medium">
          <InsightHeader theme={theme}>
            <InsightIcon theme={theme} color={theme.colors.primary}>
              <Icon name="pie-chart" size={20} color={theme.colors.primary} />
            </InsightIcon>
            <InsightTitle theme={theme}>Top Spending Categories</InsightTitle>
          </InsightHeader>

          {topCategories.length > 0 ? (
            topCategories.map((item, index) => (
              <CategoryItem key={index} theme={theme}>
                <CategoryInfo theme={theme}>
                  <CategoryIcon theme={theme} color={item.category.color}>
                    <Icon
                      name={item.category.icon}
                      size={16}
                      color={item.category.color}
                    />
                  </CategoryIcon>
                  <CategoryName theme={theme}>
                    {item.category.name}
                  </CategoryName>
                </CategoryInfo>
                <CategoryAmount theme={theme}>
                  ${item.amount.toFixed(2)}
                </CategoryAmount>
              </CategoryItem>
            ))
          ) : (
            <InsightDescription theme={theme}>
              No expense data available yet. Start tracking your expenses to see
              insights!
            </InsightDescription>
          )}
        </InsightCard>

        <SectionTitle theme={theme}>Fitness Overview</SectionTitle>

        <StatsGrid theme={theme}>
          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.primary}>
              {totalWorkouts}
            </StatValue>
            <StatLabel theme={theme}>Total Workouts</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.warning}>
              {totalCalories}
            </StatValue>
            <StatLabel theme={theme}>Calories Burned</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.info}>
              {(totalWater / 1000).toFixed(1)}L
            </StatValue>
            <StatLabel theme={theme}>Total Water</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.secondary}>
              {thisMonthWorkouts}
            </StatValue>
            <StatLabel theme={theme}>This Month</StatLabel>
          </StatCard>
        </StatsGrid>

        <InsightCard theme={theme} shadow="medium">
          <InsightHeader theme={theme}>
            <InsightIcon theme={theme} color={theme.colors.success}>
              <Icon name="trending-up" size={20} color={theme.colors.success} />
            </InsightIcon>
            <InsightTitle theme={theme}>Health Insights</InsightTitle>
          </InsightHeader>

          <InsightValue theme={theme} color={theme.colors.primary}>
            {avgCaloriesPerWorkout} cal/workout
          </InsightValue>
          <InsightDescription theme={theme}>
            Average calories burned per workout session
          </InsightDescription>

          {weightChange !== 0 && (
            <>
              <InsightValue
                theme={theme}
                color={
                  weightChange > 0 ? theme.colors.warning : theme.colors.success
                }
              >
                {weightChange > 0 ? "+" : ""}
                {weightChange.toFixed(1)} kg
              </InsightValue>
              <InsightDescription theme={theme}>
                Weight change since you started tracking
              </InsightDescription>
            </>
          )}
        </InsightCard>

        <InsightCard theme={theme} shadow="medium">
          <InsightHeader theme={theme}>
            <InsightIcon theme={theme} color={theme.colors.info}>
              <Icon name="bulb" size={20} color={theme.colors.info} />
            </InsightIcon>
            <InsightTitle theme={theme}>Smart Recommendations</InsightTitle>
          </InsightHeader>

          <InsightDescription theme={theme}>
            {balance < 0
              ? "💡 Your expenses exceed income. Consider reviewing your spending habits and creating a budget."
              : balance > totalIncome * 0.8
              ? "💡 Great job saving money! Consider investing your surplus for long-term growth."
              : "💡 You're maintaining a healthy balance. Keep tracking to stay on top of your finances."}
            {"\n\n"}
            {totalWorkouts < 4
              ? "🏃‍♂️ Try to aim for at least 4 workouts per week for optimal health benefits."
              : "🏃‍♂️ Excellent workout consistency! Keep up the great work."}
            {"\n\n"}
            {avgWaterPerDay < 1500
              ? "💧 Remember to stay hydrated! Aim for at least 2L of water daily."
              : "💧 Great hydration habits! You're meeting your daily water goals."}
          </InsightDescription>
        </InsightCard>

        <ChartContainer theme={theme}>
          <ChartPlaceholder theme={theme}>
            <Icon
              name="bar-chart"
              size={60}
              color={theme.colors.textSecondary}
            />
            <ChartText theme={theme}>
              Interactive charts coming soon!{"\n"}
              Victory Native charts will be integrated here
            </ChartText>
          </ChartPlaceholder>
        </ChartContainer>
      </ContentContainer>
    </Container>
  );
};

export default AnalyticsScreen;
