import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
} from "react-native";
import styled from "styled-components/native";
import { useAppSelector, useAppDispatch } from "../store";
import { fetchExpenses } from "../store/slices/expenseSlice";
import {
  fetchWorkouts,
  fetchWaterIntakes,
  fetchWeightEntries,
} from "../store/slices/fitnessSlice";
import { Card } from "../components/common/Card";
import Icon from "react-native-vector-icons/Ionicons";

const Container = styled(View)<{ theme: any }>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
`;

const Header = styled(View)<{ theme: any }>`
  padding: ${(props) => props.theme.spacing.lg}px;
  background-color: ${(props) => props.theme.colors.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${(props) => props.theme.colors.border};
`;

const HeaderTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h2.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h2.fontWeight};
  color: ${(props) => props.theme.colors.text};
  text-align: center;
`;

const ContentContainer = styled(ScrollView)<{ theme: any }>`
  flex: 1;
  padding: ${(props) => props.theme.spacing.lg}px;
`;

const SectionTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  margin-top: ${(props) => props.theme.spacing.lg}px;
`;

const StatsGrid = styled(View)<{ theme: any }>`
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const StatCard = styled(Card)<{ theme: any }>`
  flex: 1;
  min-width: 45%;
  margin: ${(props) => props.theme.spacing.sm}px;
  align-items: center;
`;

const StatValue = styled(Text)<{ theme: any; color?: string }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.color || props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.xs}px;
`;

const StatLabel = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
`;

const InsightCard = styled(Card)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.md}px;
`;

const InsightHeader = styled(View)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
`;

const InsightIcon = styled(View)<{ theme: any; color: string }>`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${(props) => props.color}20;
  align-items: center;
  justify-content: center;
  margin-right: ${(props) => props.theme.spacing.md}px;
`;

const InsightTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  flex: 1;
`;

const InsightDescription = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
  line-height: 20px;
`;

const AnalyticsScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { expenses } = useAppSelector((state) => state.expenses);
  const { workouts, waterIntakes, weightEntries } = useAppSelector(
    (state) => state.fitness
  );
  const theme = useAppSelector((state) => state.theme.theme);
  
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [dispatch, user]);

  const loadData = async () => {
    if (user) {
      await Promise.all([
        dispatch(fetchExpenses(user.id)),
        dispatch(fetchWorkouts(user.id)),
        dispatch(fetchWaterIntakes(user.id)),
        dispatch(fetchWeightEntries(user.id)),
      ]);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Calculate analytics data
  const totalExpenses = expenses
    .filter((e) => e.type === "expense")
    .reduce((sum, expense) => sum + expense.amount, 0);

  const totalIncome = expenses
    .filter((e) => e.type === "income")
    .reduce((sum, expense) => sum + expense.amount, 0);

  const balance = totalIncome - totalExpenses;
  const totalWorkouts = workouts.length;
  const totalCalories = workouts.reduce((sum, w) => sum + w.totalCalories, 0);
  const totalWater = waterIntakes.reduce((sum, w) => sum + w.amount, 0);

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <HeaderTitle theme={theme}>Analytics & Insights</HeaderTitle>
      </Header>

      <ContentContainer 
        theme={theme} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <SectionTitle theme={theme}>Financial Overview</SectionTitle>
        
        <StatsGrid theme={theme}>
          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.error}>
              ${totalExpenses.toFixed(2)}
            </StatValue>
            <StatLabel theme={theme}>Total Expenses</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.success}>
              ${totalIncome.toFixed(2)}
            </StatValue>
            <StatLabel theme={theme}>Total Income</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue 
              theme={theme} 
              color={balance >= 0 ? theme.colors.success : theme.colors.error}
            >
              ${balance.toFixed(2)}
            </StatValue>
            <StatLabel theme={theme}>Net Balance</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.primary}>
              {totalWorkouts}
            </StatValue>
            <StatLabel theme={theme}>Total Workouts</StatLabel>
          </StatCard>
        </StatsGrid>

        <SectionTitle theme={theme}>Fitness Overview</SectionTitle>
        
        <StatsGrid theme={theme}>
          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.warning}>
              {totalCalories}
            </StatValue>
            <StatLabel theme={theme}>Calories Burned</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatValue theme={theme} color={theme.colors.info}>
              {(totalWater / 1000).toFixed(1)}L
            </StatValue>
            <StatLabel theme={theme}>Total Water</StatLabel>
          </StatCard>
        </StatsGrid>

        <InsightCard theme={theme} shadow="medium">
          <InsightHeader theme={theme}>
            <InsightIcon theme={theme} color={theme.colors.info}>
              <Icon name="bulb" size={20} color={theme.colors.info} />
            </InsightIcon>
            <InsightTitle theme={theme}>Smart Recommendations</InsightTitle>
          </InsightHeader>
          
          <InsightDescription theme={theme}>
            {balance < 0 
              ? "💡 Your expenses exceed income. Consider reviewing your spending habits."
              : "💡 You're maintaining a healthy balance. Keep tracking your finances!"
            }
            {'\n\n'}
            {totalWorkouts < 4 
              ? "🏃‍♂️ Try to aim for at least 4 workouts per week for optimal health."
              : "🏃‍♂️ Excellent workout consistency! Keep up the great work."
            }
            {'\n\n'}
            💧 Stay hydrated! You've consumed {(totalWater / 1000).toFixed(1)}L of water so far.
          </InsightDescription>
        </InsightCard>
      </ContentContainer>
    </Container>
  );
};

export default AnalyticsScreen;
