import React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import styled from 'styled-components/native';
import { useAppSelector, useAppDispatch } from '../../store';
import { logoutUser } from '../../store/slices/authSlice';
import { Card } from '../../components/common/Card';
import { Button } from '../../components/common/Button';
import Icon from 'react-native-vector-icons/Ionicons';

const Container = styled(View)<{ theme: any }>`
  flex: 1;
  background-color: ${props => props.theme.colors.background};
  padding: ${props => props.theme.spacing.lg}px;
`;

const ProfileHeader = styled(Card)<{ theme: any }>`
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg}px;
`;

const Avatar = styled(View)<{ theme: any }>`
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: ${props => props.theme.colors.primary};
  align-items: center;
  justify-content: center;
  margin-bottom: ${props => props.theme.spacing.md}px;
`;

const AvatarText = styled(Text)<{ theme: any }>`
  font-size: 32px;
  font-weight: bold;
  color: #FFFFFF;
`;

const UserName = styled(Text)<{ theme: any }>`
  font-size: ${props => props.theme.typography.h2.fontSize}px;
  font-weight: ${props => props.theme.typography.h2.fontWeight};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.sm}px;
`;

const UserEmail = styled(Text)<{ theme: any }>`
  font-size: ${props => props.theme.typography.body.fontSize}px;
  color: ${props => props.theme.colors.textSecondary};
`;

const MenuSection = styled(View)<{ theme: any }>`
  margin-bottom: ${props => props.theme.spacing.lg}px;
`;

const MenuItem = styled(TouchableOpacity)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  padding: ${props => props.theme.spacing.lg}px;
  background-color: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.md}px;
  margin-bottom: ${props => props.theme.spacing.sm}px;
  border-width: 1px;
  border-color: ${props => props.theme.colors.border};
`;

const MenuIcon = styled(View)<{ theme: any }>`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${props => props.theme.colors.primary}20;
  align-items: center;
  justify-content: center;
  margin-right: ${props => props.theme.spacing.md}px;
`;

const MenuText = styled(Text)<{ theme: any }>`
  flex: 1;
  font-size: ${props => props.theme.typography.body.fontSize}px;
  color: ${props => props.theme.colors.text};
`;

const ProfileScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const theme = useAppSelector(state => state.theme.theme);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: () => dispatch(logoutUser())
        },
      ]
    );
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Container theme={theme}>
      <ProfileHeader theme={theme} shadow="medium">
        <Avatar theme={theme}>
          <AvatarText theme={theme}>
            {user?.name ? getInitials(user.name) : 'U'}
          </AvatarText>
        </Avatar>
        <UserName theme={theme}>{user?.name}</UserName>
        <UserEmail theme={theme}>{user?.email}</UserEmail>
      </ProfileHeader>

      <MenuSection theme={theme}>
        <MenuItem theme={theme}>
          <MenuIcon theme={theme}>
            <Icon name="person-outline" size={20} color={theme.colors.primary} />
          </MenuIcon>
          <MenuText theme={theme}>Edit Profile</MenuText>
          <Icon name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </MenuItem>

        <MenuItem theme={theme}>
          <MenuIcon theme={theme}>
            <Icon name="notifications-outline" size={20} color={theme.colors.primary} />
          </MenuIcon>
          <MenuText theme={theme}>Notifications</MenuText>
          <Icon name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </MenuItem>

        <MenuItem theme={theme}>
          <MenuIcon theme={theme}>
            <Icon name="shield-outline" size={20} color={theme.colors.primary} />
          </MenuIcon>
          <MenuText theme={theme}>Privacy & Security</MenuText>
          <Icon name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </MenuItem>

        <MenuItem theme={theme}>
          <MenuIcon theme={theme}>
            <Icon name="help-circle-outline" size={20} color={theme.colors.primary} />
          </MenuIcon>
          <MenuText theme={theme}>Help & Support</MenuText>
          <Icon name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </MenuItem>

        <MenuItem theme={theme}>
          <MenuIcon theme={theme}>
            <Icon name="information-circle-outline" size={20} color={theme.colors.primary} />
          </MenuIcon>
          <MenuText theme={theme}>About</MenuText>
          <Icon name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </MenuItem>
      </MenuSection>

      <Button
        title="Logout"
        onPress={handleLogout}
        variant="outline"
        icon={<Icon name="log-out-outline" size={20} color={theme.colors.primary} />}
      />
    </Container>
  );
};

export default ProfileScreen;
