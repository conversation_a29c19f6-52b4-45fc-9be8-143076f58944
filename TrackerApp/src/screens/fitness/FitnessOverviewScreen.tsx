import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
} from "react-native";
import styled from "styled-components/native";
import { useAppSelector, useAppDispatch } from "../../store";
import {
  fetchWorkouts,
  addWorkout,
  fetchWaterIntakes,
  addWaterIntake,
  fetchWeightEntries,
  addWeightEntry,
} from "../../store/slices/fitnessSlice";
import { Card } from "../../components/common/Card";
import Icon from "react-native-vector-icons/Ionicons";
import { safeConsoleError } from "../../utils/errorUtils";

const Container = styled(View)<{ theme: any }>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
`;

const Header = styled(View)<{ theme: any }>`
  padding: ${(props) => props.theme.spacing.lg}px;
  background-color: ${(props) => props.theme.colors.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${(props) => props.theme.colors.border};
`;

const HeaderTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h2.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h2.fontWeight};
  color: ${(props) => props.theme.colors.text};
  text-align: center;
`;

const ContentContainer = styled(ScrollView)<{ theme: any }>`
  flex: 1;
  padding: ${(props) => props.theme.spacing.lg}px;
`;

const SectionTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  margin-top: ${(props) => props.theme.spacing.lg}px;
`;

const StatsGrid = styled(View)<{ theme: any }>`
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
`;

const StatCard = styled(Card)<{ theme: any }>`
  flex: 1;
  min-width: 45%;
  margin: ${(props) => props.theme.spacing.sm}px;
  align-items: center;
`;

const StatIcon = styled(View)<{ theme: any; color: string }>`
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: ${(props) => props.color}20;
  align-items: center;
  justify-content: center;
  margin-bottom: ${(props) => props.theme.spacing.sm}px;
`;

const StatValue = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h2.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h2.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.xs}px;
`;

const StatLabel = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
`;

const ActionCard = styled(Card)<{ theme: any }>`
  margin-bottom: ${(props) => props.theme.spacing.md}px;
  flex-direction: row;
  align-items: center;
`;

const ActionIcon = styled(View)<{ theme: any; color: string }>`
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: ${(props) => props.color}20;
  align-items: center;
  justify-content: center;
  margin-right: ${(props) => props.theme.spacing.md}px;
`;

const ActionContent = styled(View)<{ theme: any }>`
  flex: 1;
`;

const ActionTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.xs}px;
`;

const ActionDescription = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
`;

const QuickActionButton = styled(TouchableOpacity)<{ theme: any }>`
  padding: ${(props) => props.theme.spacing.sm}px;
  background-color: ${(props) => props.theme.colors.primary};
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  flex: 1;
  min-height: 44px;
`;

const QuickActionText = styled(Text)<{ theme: any }>`
  color: #ffffff;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  text-align: center;
  flex-shrink: 1;
  flex-wrap: wrap;
`;

const ResponsiveButtonContainer = styled(View)<{ theme: any }>`
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: ${(props) => props.theme.spacing.sm}px;
  gap: 6px;
  justify-content: space-between;
  align-items: stretch;
  min-height: 44px;
`;

const WaterProgress = styled(View)<{ theme: any }>`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: ${(props) => props.theme.spacing.md}px;
  padding: ${(props) => props.theme.spacing.sm}px;
  background-color: ${(props) => props.theme.colors.background};
  border-radius: ${(props) => props.theme.borderRadius.lg}px;
  border: 1px solid ${(props) => props.theme.colors.border};
  flex-wrap: wrap;
  min-height: 60px;
`;

const WaterGlass = styled(TouchableOpacity)<{ theme: any; filled: boolean }>`
  flex: 1;
  min-width: 80px;
  max-width: 120px;
  height: 60px;
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  border-width: 2px;
  border-color: ${(props) =>
    props.filled ? props.theme.colors.info : props.theme.colors.border};
  background-color: ${(props) =>
    props.filled ? props.theme.colors.info + "20" : "transparent"};
  margin: ${(props) => props.theme.spacing.xs}px;
  padding: ${(props) => props.theme.spacing.sm}px;
  align-items: center;
  justify-content: center;
  shadow-color: ${(props) =>
    props.filled ? props.theme.colors.info : "transparent"};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.2;
  shadow-radius: 4px;
  elevation: ${(props) => (props.filled ? 3 : 1)};
  transform: ${(props) => (props.filled ? "scale(1.02)" : "scale(1)")};
`;

const WaterDroplet = styled(View)<{ theme: any; filled: boolean }>`
  width: 12px;
  height: 16px;
  background-color: ${(props) =>
    props.filled ? props.theme.colors.info : props.theme.colors.border};
  border-radius: 9px 9px 9px 0;
  transform: rotate(45deg);
  opacity: ${(props) => (props.filled ? 1 : 0.4)};
`;

const WaterAmount = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
  margin-top: ${(props) => props.theme.spacing.xs}px;
  flex-wrap: wrap;
`;

const WaterGoalContainer = styled(View)<{ theme: any }>`
  margin-top: ${(props) => props.theme.spacing.sm}px;
  align-items: center;
`;

const WaterGoalText = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.caption.fontSize}px;
  color: ${(props) => props.theme.colors.textSecondary};
  text-align: center;
`;

const WaterGoalProgress = styled(View)<{ theme: any }>`
  width: 100%;
  height: 6px;
  background-color: ${(props) => props.theme.colors.border};
  border-radius: 3px;
  margin-top: ${(props) => props.theme.spacing.xs}px;
  overflow: hidden;
`;

const WaterGoalFill = styled(View)<{ theme: any; progress: number }>`
  height: 100%;
  width: ${(props) => Math.min(props.progress, 100)}%;
  background-color: ${(props) => props.theme.colors.info};
  border-radius: 3px;
  transition: width 0.3s ease;
`;

// Modal Components
const ModalOverlay = styled(Modal)``;

const ModalContainer = styled(View)<{ theme: any }>`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: ${(props) => props.theme.spacing.lg}px;
`;

const ModalContent = styled(View)<{ theme: any }>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.borderRadius.lg}px;
  padding: ${(props) => props.theme.spacing.lg}px;
  width: 100%;
  max-width: 400px;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.3;
  shadow-radius: 8px;
  elevation: 8;
`;

const ModalTitle = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.h3.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  text-align: center;
  margin-bottom: ${(props) => props.theme.spacing.md}px;
`;

const ModalLabel = styled(Text)<{ theme: any }>`
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  color: ${(props) => props.theme.colors.text};
  margin-bottom: ${(props) => props.theme.spacing.sm}px;
`;

const ModalInput = styled(TextInput)<{ theme: any }>`
  border: 2px solid ${(props) => props.theme.colors.border};
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  padding: ${(props) => props.theme.spacing.md}px;
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
  color: ${(props) => props.theme.colors.text};
  background-color: ${(props) => props.theme.colors.background};
  margin-bottom: ${(props) => props.theme.spacing.lg}px;
  text-align: center;
`;

const ModalButtonContainer = styled(View)<{ theme: any }>`
  flex-direction: row;
  gap: ${(props) => props.theme.spacing.md}px;
`;

const ModalButton = styled(TouchableOpacity)<{
  theme: any;
  variant: "primary" | "secondary";
}>`
  flex: 1;
  padding: ${(props) => props.theme.spacing.md}px;
  border-radius: ${(props) => props.theme.borderRadius.md}px;
  background-color: ${(props) =>
    props.variant === "primary"
      ? props.theme.colors.primary
      : props.theme.colors.border};
  align-items: center;
`;

const ModalButtonText = styled(Text)<{
  theme: any;
  variant: "primary" | "secondary";
}>`
  color: ${(props) =>
    props.variant === "primary" ? "#FFFFFF" : props.theme.colors.text};
  font-weight: ${(props) => props.theme.typography.h3.fontWeight};
  font-size: ${(props) => props.theme.typography.body.fontSize}px;
`;

const FitnessOverviewScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { workouts, waterIntakes, weightEntries } = useAppSelector(
    (state) => state.fitness
  );
  const theme = useAppSelector((state) => state.theme.theme);

  const [refreshing, setRefreshing] = useState(false);
  const [weightModalVisible, setWeightModalVisible] = useState(false);
  const [weightInput, setWeightInput] = useState("");
  const [customWorkoutModalVisible, setCustomWorkoutModalVisible] =
    useState(false);
  const [workoutName, setWorkoutName] = useState("");
  const [workoutDuration, setWorkoutDuration] = useState("");

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [dispatch, user]);

  const loadData = async () => {
    if (user) {
      await Promise.all([
        dispatch(fetchWorkouts(user.id)),
        dispatch(fetchWaterIntakes(user.id)),
        dispatch(fetchWeightEntries(user.id)),
      ]);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Calculate today's stats
  const today = new Date().toISOString().split("T")[0];
  const todayWorkouts = workouts.filter((w) => w.date.startsWith(today));
  const todayWater = waterIntakes.filter((w) => w.date.startsWith(today));
  const totalWaterToday = todayWater.reduce((sum, w) => sum + w.amount, 0);
  const waterGoal = 2000; // 2L daily goal

  const totalCalories = workouts.reduce((sum, w) => sum + w.totalCalories, 0);
  const currentWeight =
    weightEntries.length > 0
      ? weightEntries[weightEntries.length - 1].weight
      : 0;

  const addQuickWorkout = async (
    name: string,
    duration: number,
    calories: number
  ) => {
    if (user) {
      const workout = {
        userId: user.id,
        name,
        exercises: [
          {
            id: Math.random().toString(36).substring(2, 11),
            name: name,
            duration,
            caloriesBurned: calories,
          },
        ],
        totalDuration: duration,
        totalCalories: calories,
        date: new Date().toISOString(),
      };
      await dispatch(addWorkout(workout));
      Alert.alert("Success", `${name} workout logged successfully!`);
    }
  };

  const handleCustomWorkout = () => {
    if (!user) {
      Alert.alert("Error", "Please log in to record your workout.");
      return;
    }
    setCustomWorkoutModalVisible(true);
  };

  const handleCustomWorkoutSave = async () => {
    if (!user) return;

    if (workoutName.trim() === "" || workoutDuration.trim() === "") {
      Alert.alert(
        "Invalid Input",
        "Please enter both workout name and duration."
      );
      return;
    }

    const duration = parseInt(workoutDuration);
    if (isNaN(duration) || duration <= 0) {
      Alert.alert(
        "Invalid Duration",
        "Please enter a valid duration in minutes."
      );
      return;
    }

    try {
      // Calculate estimated calories (5 calories per minute as base rate)
      const estimatedCalories = duration * 5;

      await addQuickWorkout(workoutName.trim(), duration, estimatedCalories);

      // Close modal and reset form
      setCustomWorkoutModalVisible(false);
      setWorkoutName("");
      setWorkoutDuration("");

      Alert.alert(
        "Success! 💪",
        `${workoutName.trim()} workout logged successfully!\nDuration: ${duration} minutes\nEstimated calories: ${estimatedCalories}`,
        [{ text: "Great!", style: "default" }]
      );
    } catch (error) {
      safeConsoleError("Error adding custom workout:", error);
      Alert.alert("Error", "Failed to save workout. Please try again.");
    }
  };

  const handleCustomWorkoutCancel = () => {
    setCustomWorkoutModalVisible(false);
    setWorkoutName("");
    setWorkoutDuration("");
  };

  const handleAddWater = (amount: number) => {
    if (user) {
      const waterIntake = {
        userId: user.id,
        amount,
        date: new Date().toISOString(),
        time: new Date().toISOString(),
      };
      dispatch(addWaterIntake(waterIntake));

      // Show success feedback
      Alert.alert(
        "Water Added! 💧",
        `Added ${amount}ml of water. Keep staying hydrated!`,
        [{ text: "Great!", style: "default" }],
        { cancelable: true }
      );
    }
  };

  const handleAddWaterGlass = () => {
    if (user) {
      handleAddWater(250);
    }
  };

  const handleRemoveWaterGlass = () => {
    if (user && totalWaterToday >= 250) {
      const waterIntake = {
        userId: user.id,
        amount: -250, // Negative amount for removal
        date: new Date().toISOString(),
        time: new Date().toISOString(),
      };
      dispatch(addWaterIntake(waterIntake));

      Alert.alert(
        "Water Removed! 💧",
        "Removed 250ml from your daily intake.",
        [{ text: "OK", style: "default" }],
        { cancelable: true }
      );
    } else {
      Alert.alert(
        "Cannot Remove",
        "You don't have enough water intake to remove 250ml.",
        [{ text: "OK", style: "default" }]
      );
    }
  };

  const handleWeightEntry = () => {
    if (!user) {
      Alert.alert("Error", "Please log in to record your weight.");
      return;
    }

    // Set initial value and show modal
    setWeightInput(currentWeight > 0 ? currentWeight.toString() : "");
    setWeightModalVisible(true);
  };

  const handleWeightSave = async () => {
    if (!user) return;

    if (weightInput && weightInput.trim() !== "") {
      const weightValue = parseFloat(weightInput);

      if (isNaN(weightValue) || weightValue <= 0) {
        Alert.alert("Invalid Weight", "Please enter a valid weight in kg.");
        return;
      }

      try {
        const weightEntry = {
          userId: user.id,
          weight: weightValue,
          date: new Date().toISOString(),
          bmi:
            user.height && user.height > 0
              ? weightValue / Math.pow(user.height / 100, 2)
              : 0,
        };

        console.log("Adding weight entry:", weightEntry);
        await dispatch(addWeightEntry(weightEntry));

        // Close modal and show success
        setWeightModalVisible(false);
        setWeightInput("");

        Alert.alert(
          "Success! ⚖️",
          `Weight recorded: ${weightValue}kg${
            weightEntry.bmi > 0 ? `\nBMI: ${weightEntry.bmi.toFixed(1)}` : ""
          }`,
          [{ text: "Great!", style: "default" }]
        );
      } catch (error) {
        console.error("Error adding weight entry:", error);
        Alert.alert("Error", "Failed to save weight entry. Please try again.");
      }
    } else {
      Alert.alert("Invalid Input", "Please enter a valid weight.");
    }
  };

  const handleWeightCancel = () => {
    setWeightModalVisible(false);
    setWeightInput("");
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <HeaderTitle theme={theme}>Fitness Overview</HeaderTitle>
      </Header>

      <ContentContainer
        theme={theme}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <SectionTitle theme={theme}>Today's Progress</SectionTitle>

        <StatsGrid theme={theme}>
          <StatCard theme={theme} shadow="small">
            <StatIcon theme={theme} color={theme.colors.primary}>
              <Icon name="fitness" size={24} color={theme.colors.primary} />
            </StatIcon>
            <StatValue theme={theme}>{todayWorkouts.length}</StatValue>
            <StatLabel theme={theme}>Workouts Today</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatIcon theme={theme} color={theme.colors.info}>
              <Icon name="water" size={24} color={theme.colors.info} />
            </StatIcon>
            <StatValue theme={theme}>{totalWaterToday}ml</StatValue>
            <StatLabel theme={theme}>Water Intake</StatLabel>

            <WaterGoalContainer theme={theme}>
              <WaterGoalText theme={theme}>
                {totalWaterToday}/{waterGoal}ml (
                {Math.round((totalWaterToday / waterGoal) * 100)}%)
              </WaterGoalText>
              <WaterGoalProgress theme={theme}>
                <WaterGoalFill
                  theme={theme}
                  progress={(totalWaterToday / waterGoal) * 100}
                />
              </WaterGoalProgress>
            </WaterGoalContainer>

            <WaterProgress theme={theme}>
              <WaterGlass
                theme={theme}
                filled={true}
                onPress={handleAddWaterGlass}
                activeOpacity={0.7}
              >
                <WaterDroplet theme={theme} filled={true} />
                <WaterAmount
                  theme={theme}
                  style={{ marginTop: 4, fontSize: 10 }}
                >
                  +250ml
                </WaterAmount>
              </WaterGlass>

              <WaterGlass
                theme={theme}
                filled={totalWaterToday >= 250}
                onPress={handleRemoveWaterGlass}
                activeOpacity={0.7}
                style={{
                  opacity: totalWaterToday >= 250 ? 1 : 0.5,
                }}
              >
                <WaterDroplet theme={theme} filled={totalWaterToday >= 250} />
                <WaterAmount
                  theme={theme}
                  style={{ marginTop: 4, fontSize: 10 }}
                >
                  -250ml
                </WaterAmount>
              </WaterGlass>
            </WaterProgress>

            <WaterAmount theme={theme}>
              Tap + to add water, tap - to remove
            </WaterAmount>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatIcon theme={theme} color={theme.colors.warning}>
              <Icon name="flame" size={24} color={theme.colors.warning} />
            </StatIcon>
            <StatValue theme={theme}>{totalCalories}</StatValue>
            <StatLabel theme={theme}>Total Calories Burned</StatLabel>
          </StatCard>

          <StatCard theme={theme} shadow="small">
            <StatIcon theme={theme} color={theme.colors.secondary}>
              <Icon name="scale" size={24} color={theme.colors.secondary} />
            </StatIcon>
            <StatValue theme={theme}>
              {currentWeight > 0 ? `${currentWeight}kg` : "--"}
            </StatValue>
            <StatLabel theme={theme}>
              {currentWeight > 0 ? "Current Weight" : "No Weight Recorded"}
            </StatLabel>
            {weightEntries.length > 1 && (
              <StatLabel theme={theme} style={{ marginTop: 4, fontSize: 10 }}>
                {weightEntries.length} entries recorded
              </StatLabel>
            )}
          </StatCard>
        </StatsGrid>

        <SectionTitle theme={theme}>Quick Actions</SectionTitle>

        <ActionCard theme={theme} shadow="small">
          <ActionIcon theme={theme} color={theme.colors.primary}>
            <Icon name="barbell" size={30} color={theme.colors.primary} />
          </ActionIcon>
          <ActionContent theme={theme}>
            <ActionTitle theme={theme}>Quick Workout</ActionTitle>
            <ActionDescription theme={theme}>
              Choose from preset workouts or create custom
            </ActionDescription>
            <ResponsiveButtonContainer theme={theme}>
              <QuickActionButton
                theme={theme}
                onPress={() => addQuickWorkout("Cardio", 30, 150)}
              >
                <QuickActionText theme={theme}>Cardio</QuickActionText>
              </QuickActionButton>
              <QuickActionButton
                theme={theme}
                onPress={() => addQuickWorkout("Strength", 45, 200)}
              >
                <QuickActionText theme={theme}>Strength</QuickActionText>
              </QuickActionButton>
              <QuickActionButton
                theme={theme}
                onPress={() => addQuickWorkout("Yoga", 60, 120)}
              >
                <QuickActionText theme={theme}>Yoga</QuickActionText>
              </QuickActionButton>
              <QuickActionButton theme={theme} onPress={handleCustomWorkout}>
                <QuickActionText theme={theme}>Custom</QuickActionText>
              </QuickActionButton>
            </ResponsiveButtonContainer>
          </ActionContent>
        </ActionCard>

        <ActionCard theme={theme} shadow="small">
          <ActionIcon theme={theme} color={theme.colors.info}>
            <Icon name="water" size={30} color={theme.colors.info} />
          </ActionIcon>
          <ActionContent theme={theme}>
            <ActionTitle theme={theme}>Quick Water Intake</ActionTitle>
            <ActionDescription theme={theme}>
              Stay hydrated! Choose your preferred amount
            </ActionDescription>
            <ResponsiveButtonContainer theme={theme}>
              <QuickActionButton
                theme={theme}
                onPress={() => handleAddWater(250)}
              >
                <QuickActionText theme={theme}>250ml</QuickActionText>
              </QuickActionButton>
              <QuickActionButton
                theme={theme}
                onPress={() => handleAddWater(500)}
              >
                <QuickActionText theme={theme}>500ml</QuickActionText>
              </QuickActionButton>
              <QuickActionButton
                theme={theme}
                onPress={() => handleAddWater(750)}
              >
                <QuickActionText theme={theme}>750ml</QuickActionText>
              </QuickActionButton>
            </ResponsiveButtonContainer>
          </ActionContent>
        </ActionCard>

        <ActionCard theme={theme} shadow="small">
          <ActionIcon theme={theme} color={theme.colors.secondary}>
            <Icon name="scale" size={30} color={theme.colors.secondary} />
          </ActionIcon>
          <ActionContent theme={theme}>
            <ActionTitle theme={theme}>Weight Tracking</ActionTitle>
            <ActionDescription theme={theme}>
              {currentWeight > 0
                ? `Current: ${currentWeight}kg • Record new weight to track progress`
                : "Record your weight to start monitoring your progress over time"}
            </ActionDescription>
          </ActionContent>
          <QuickActionButton theme={theme} onPress={handleWeightEntry}>
            <QuickActionText theme={theme}>
              {currentWeight > 0 ? "Update" : "Add Weight"}
            </QuickActionText>
          </QuickActionButton>
        </ActionCard>

        <SectionTitle theme={theme}>Daily Motivation</SectionTitle>

        <ActionCard theme={theme} shadow="small">
          <ActionIcon theme={theme} color={theme.colors.success}>
            <Icon name="trophy" size={30} color={theme.colors.success} />
          </ActionIcon>
          <ActionContent theme={theme}>
            <ActionTitle theme={theme}>
              {totalWaterToday >= waterGoal
                ? "🎉 Hydration Goal Achieved!"
                : todayWorkouts.length >= 1
                ? "💪 Great Job Today!"
                : "🌟 Ready to Start Your Day?"}
            </ActionTitle>
            <ActionDescription theme={theme}>
              {totalWaterToday >= waterGoal && todayWorkouts.length >= 1
                ? "Amazing! You've hit your water goal and completed workouts today. You're crushing it!"
                : totalWaterToday >= waterGoal
                ? "Excellent hydration today! Now let's add some movement to complete your wellness routine."
                : todayWorkouts.length >= 1
                ? "Great workout session! Don't forget to stay hydrated throughout the day."
                : "Every journey begins with a single step. Start with a quick workout or glass of water!"}
            </ActionDescription>
          </ActionContent>
        </ActionCard>
      </ContentContainer>

      {/* Weight Entry Modal */}
      <ModalOverlay
        visible={weightModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleWeightCancel}
      >
        <ModalContainer theme={theme}>
          <ModalContent theme={theme}>
            <ModalTitle theme={theme}>⚖️ Weight Entry</ModalTitle>
            <ModalInput
              theme={theme}
              value={weightInput}
              onChangeText={setWeightInput}
              placeholder="Enter weight in kg"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
              autoFocus={true}
              selectTextOnFocus={true}
            />
            <ModalButtonContainer theme={theme}>
              <ModalButton
                theme={theme}
                variant="secondary"
                onPress={handleWeightCancel}
              >
                <ModalButtonText theme={theme} variant="secondary">
                  Cancel
                </ModalButtonText>
              </ModalButton>
              <ModalButton
                theme={theme}
                variant="primary"
                onPress={handleWeightSave}
              >
                <ModalButtonText theme={theme} variant="primary">
                  Save
                </ModalButtonText>
              </ModalButton>
            </ModalButtonContainer>
          </ModalContent>
        </ModalContainer>
      </ModalOverlay>

      {/* Custom Workout Modal */}
      <ModalOverlay
        visible={customWorkoutModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCustomWorkoutCancel}
      >
        <ModalContainer theme={theme}>
          <ModalContent theme={theme}>
            <ScrollView showsVerticalScrollIndicator={false}>
              <ModalTitle theme={theme}>💪 Custom Workout</ModalTitle>

              <ModalLabel theme={theme}>Workout Name</ModalLabel>
              <ModalInput
                theme={theme}
                value={workoutName}
                onChangeText={setWorkoutName}
                placeholder="e.g., Running, Swimming, Dancing"
                placeholderTextColor={theme.colors.textSecondary}
                autoFocus={true}
              />

              <ModalLabel theme={theme}>Duration (minutes)</ModalLabel>
              <ModalInput
                theme={theme}
                value={workoutDuration}
                onChangeText={setWorkoutDuration}
                placeholder="e.g., 30"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
              />

              <ModalButtonContainer theme={theme}>
                <ModalButton
                  theme={theme}
                  variant="secondary"
                  onPress={handleCustomWorkoutCancel}
                >
                  <ModalButtonText theme={theme} variant="secondary">
                    Cancel
                  </ModalButtonText>
                </ModalButton>
                <ModalButton
                  theme={theme}
                  variant="primary"
                  onPress={handleCustomWorkoutSave}
                >
                  <ModalButtonText theme={theme} variant="primary">
                    Save Workout
                  </ModalButtonText>
                </ModalButton>
              </ModalButtonContainer>
            </ScrollView>
          </ModalContent>
        </ModalContainer>
      </ModalOverlay>
    </Container>
  );
};

export default FitnessOverviewScreen;
