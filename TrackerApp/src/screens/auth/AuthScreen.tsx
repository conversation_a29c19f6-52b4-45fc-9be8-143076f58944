import React, { useState } from 'react';
import { View, Text, ScrollView, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import styled from 'styled-components/native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAppSelector, useAppDispatch } from '../../store';
import { loginUser, registerUser, clearError } from '../../store/slices/authSlice';
import { Button } from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import { Card } from '../../components/common/Card';
import Icon from 'react-native-vector-icons/Ionicons';

const Container = styled(View)<{ theme: any }>`
  flex: 1;
  background-color: ${props => props.theme.colors.background};
`;

const ContentContainer = styled(ScrollView)<{ theme: any }>`
  flex: 1;
  padding: ${props => props.theme.spacing.lg}px;
`;

const HeaderContainer = styled(View)<{ theme: any }>`
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.xl}px;
  margin-top: ${props => props.theme.spacing.xl * 2}px;
`;

const AppTitle = styled(Text)<{ theme: any }>`
  font-size: ${props => props.theme.typography.h1.fontSize}px;
  font-weight: ${props => props.theme.typography.h1.fontWeight};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.sm}px;
`;

const AppSubtitle = styled(Text)<{ theme: any }>`
  font-size: ${props => props.theme.typography.body.fontSize}px;
  color: ${props => props.theme.colors.textSecondary};
  text-align: center;
`;

const FormContainer = styled(View)<{ theme: any }>`
  margin-bottom: ${props => props.theme.spacing.lg}px;
`;

const TabContainer = styled(View)<{ theme: any }>`
  flex-direction: row;
  margin-bottom: ${props => props.theme.spacing.lg}px;
  background-color: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.md}px;
  padding: ${props => props.theme.spacing.sm}px;
`;

const TabButton = styled.TouchableOpacity<{ theme: any; active: boolean }>`
  flex: 1;
  padding: ${props => props.theme.spacing.md}px;
  border-radius: ${props => props.theme.borderRadius.sm}px;
  background-color: ${props => props.active ? props.theme.colors.primary : 'transparent'};
  align-items: center;
`;

const TabText = styled(Text)<{ theme: any; active: boolean }>`
  font-size: ${props => props.theme.typography.body.fontSize}px;
  font-weight: ${props => props.theme.typography.h3.fontWeight};
  color: ${props => props.active ? '#FFFFFF' : props.theme.colors.text};
`;

const DemoContainer = styled(View)<{ theme: any }>`
  margin-top: ${props => props.theme.spacing.lg}px;
  padding: ${props => props.theme.spacing.md}px;
  background-color: ${props => props.theme.colors.info}20;
  border-radius: ${props => props.theme.borderRadius.md}px;
  border-left-width: 4px;
  border-left-color: ${props => props.theme.colors.info};
`;

const DemoText = styled(Text)<{ theme: any }>`
  font-size: ${props => props.theme.typography.caption.fontSize}px;
  color: ${props => props.theme.colors.text};
  line-height: 20px;
`;

interface LoginFormData {
  email: string;
  password: string;
}

interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

const loginSchema = yup.object().shape({
  email: yup.string().email('Invalid email').required('Email is required'),
  password: yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
});

const registerSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  password: yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
  confirmPassword: yup.string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Confirm password is required'),
});

const AuthScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector(state => state.auth);
  const theme = useAppSelector(state => state.theme.theme);
  const [isLogin, setIsLogin] = useState(true);

  const loginForm = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: '<EMAIL>',
      password: 'demo123',
    },
  });

  const registerForm = useForm<RegisterFormData>({
    resolver: yupResolver(registerSchema),
  });

  const onLogin = async (data: LoginFormData) => {
    try {
      await dispatch(loginUser(data)).unwrap();
    } catch (error) {
      Alert.alert('Login Failed', error as string);
    }
  };

  const onRegister = async (data: RegisterFormData) => {
    try {
      await dispatch(registerUser({
        email: data.email,
        password: data.password,
        name: data.name,
      })).unwrap();
    } catch (error) {
      Alert.alert('Registration Failed', error as string);
    }
  };

  const switchTab = (login: boolean) => {
    setIsLogin(login);
    dispatch(clearError());
    loginForm.reset();
    registerForm.reset();
  };

  return (
    <Container theme={theme}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ContentContainer theme={theme} showsVerticalScrollIndicator={false}>
          <HeaderContainer theme={theme}>
            <Icon name="fitness" size={60} color={theme.colors.primary} />
            <AppTitle theme={theme}>Tracker App</AppTitle>
            <AppSubtitle theme={theme}>
              Your all-in-one expense and fitness tracker
            </AppSubtitle>
          </HeaderContainer>

          <Card shadow="medium">
            <TabContainer theme={theme}>
              <TabButton
                theme={theme}
                active={isLogin}
                onPress={() => switchTab(true)}
              >
                <TabText theme={theme} active={isLogin}>Login</TabText>
              </TabButton>
              <TabButton
                theme={theme}
                active={!isLogin}
                onPress={() => switchTab(false)}
              >
                <TabText theme={theme} active={!isLogin}>Register</TabText>
              </TabButton>
            </TabContainer>

            <FormContainer theme={theme}>
              {isLogin ? (
                <>
                  <Controller
                    control={loginForm.control}
                    name="email"
                    render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                      <Input
                        label="Email"
                        placeholder="Enter your email"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        error={error?.message}
                        leftIcon="mail-outline"
                        keyboardType="email-address"
                        autoCapitalize="none"
                      />
                    )}
                  />
                  <Controller
                    control={loginForm.control}
                    name="password"
                    render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                      <Input
                        label="Password"
                        placeholder="Enter your password"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        error={error?.message}
                        leftIcon="lock-closed-outline"
                        secureTextEntry
                      />
                    )}
                  />
                  <Button
                    title="Login"
                    onPress={loginForm.handleSubmit(onLogin)}
                    loading={isLoading}
                  />
                </>
              ) : (
                <>
                  <Controller
                    control={registerForm.control}
                    name="name"
                    render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                      <Input
                        label="Full Name"
                        placeholder="Enter your full name"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        error={error?.message}
                        leftIcon="person-outline"
                      />
                    )}
                  />
                  <Controller
                    control={registerForm.control}
                    name="email"
                    render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                      <Input
                        label="Email"
                        placeholder="Enter your email"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        error={error?.message}
                        leftIcon="mail-outline"
                        keyboardType="email-address"
                        autoCapitalize="none"
                      />
                    )}
                  />
                  <Controller
                    control={registerForm.control}
                    name="password"
                    render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                      <Input
                        label="Password"
                        placeholder="Enter your password"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        error={error?.message}
                        leftIcon="lock-closed-outline"
                        secureTextEntry
                      />
                    )}
                  />
                  <Controller
                    control={registerForm.control}
                    name="confirmPassword"
                    render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                      <Input
                        label="Confirm Password"
                        placeholder="Confirm your password"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        error={error?.message}
                        leftIcon="lock-closed-outline"
                        secureTextEntry
                      />
                    )}
                  />
                  <Button
                    title="Register"
                    onPress={registerForm.handleSubmit(onRegister)}
                    loading={isLoading}
                  />
                </>
              )}
            </FormContainer>
          </Card>

          {isLogin && (
            <DemoContainer theme={theme}>
              <DemoText theme={theme}>
                <Text style={{ fontWeight: 'bold' }}>Demo Account:</Text>{'\n'}
                Email: <EMAIL>{'\n'}
                Password: demo123
              </DemoText>
            </DemoContainer>
          )}
        </ContentContainer>
      </KeyboardAvoidingView>
    </Container>
  );
};

export default AuthScreen;
