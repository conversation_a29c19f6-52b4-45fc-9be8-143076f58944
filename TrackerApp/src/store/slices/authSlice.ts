import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AuthState, User } from '../../types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { clearUserData } from '../../utils/storage';

// Mock authentication service - replace with real API calls
const authService = {
  login: async (email: string, password: string): Promise<User> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (email === '<EMAIL>' && password === 'demo123') {
      const user: User = {
        id: '1',
        email,
        name: 'Demo User',
        weight: 70,
        height: 175,
        age: 25,
        fitnessGoal: 'General Fitness',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      await AsyncStorage.setItem('user', JSON.stringify(user));
      return user;
    }
    throw new Error('Invalid credentials');
  },

  register: async (email: string, password: string, name: string): Promise<User> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    const user: User = {
      id: Date.now().toString(),
      email,
      name,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    await AsyncStorage.setItem('user', JSON.stringify(user));
    return user;
  },

  logout: async (): Promise<void> => {
    await AsyncStorage.removeItem('user');
  },

  getCurrentUser: async (): Promise<User | null> => {
    try {
      const userData = await AsyncStorage.getItem('user');
      if (!userData) return null;

      const user = JSON.parse(userData);

      // Check if user data has old Date format and clear if needed
      if (user.createdAt && typeof user.createdAt === 'object') {
        console.log('Clearing old user data format');
        await clearUserData();
        return null;
      }

      return user;
    } catch (error) {
      console.error('Error getting current user:', error);
      await clearUserData();
      return null;
    }
  },

  updateProfile: async (userId: string, updates: Partial<User>): Promise<User> => {
    const userData = await AsyncStorage.getItem('user');
    if (!userData) throw new Error('User not found');

    const user = JSON.parse(userData);
    const updatedUser = { ...user, ...updates, updatedAt: new Date().toISOString() };
    await AsyncStorage.setItem('user', JSON.stringify(updatedUser));
    return updatedUser;
  },
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async ({ email, password }: { email: string; password: string }) => {
    return await authService.login(email, password);
  }
);

export const registerUser = createAsyncThunk(
  'auth/register',
  async ({ email, password, name }: { email: string; password: string; name: string }) => {
    return await authService.register(email, password, name);
  }
);

export const logoutUser = createAsyncThunk('auth/logout', async () => {
  await authService.logout();
});

export const loadUser = createAsyncThunk('auth/loadUser', async () => {
  return await authService.getCurrentUser();
});

export const updateUserProfile = createAsyncThunk(
  'auth/updateProfile',
  async ({ userId, updates }: { userId: string; updates: Partial<User> }) => {
    return await authService.updateProfile(userId, updates);
  }
);

export const clearStorageData = createAsyncThunk('auth/clearStorage', async () => {
  await clearUserData();
});

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Login failed';
      })
      // Register
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Registration failed';
      })
      // Logout
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.isAuthenticated = false;
        state.error = null;
      })
      // Load user
      .addCase(loadUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loadUser.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          state.user = action.payload;
          state.isAuthenticated = true;
        }
      })
      .addCase(loadUser.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
      })
      // Update profile
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.user = action.payload;
      });
  },
});

export const { clearError, setUser } = authSlice.actions;
export default authSlice.reducer;
