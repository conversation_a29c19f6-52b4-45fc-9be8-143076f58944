import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ExpenseState, Expense, Budget, ExpenseCategory, SalarySettings } from '../../types';
import { expenseCategories } from '../../constants/theme';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock expense service
const expenseService = {
  getExpenses: async (userId: string): Promise<Expense[]> => {
    const data = await AsyncStorage.getItem(`expenses_${userId}`);
    return data ? JSON.parse(data) : [];
  },

  addExpense: async (expense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>): Promise<Expense> => {
    const newExpense: Expense = {
      ...expense,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const existingExpenses = await expenseService.getExpenses(expense.userId);
    const updatedExpenses = [...existingExpenses, newExpense];
    await AsyncStorage.setItem(`expenses_${expense.userId}`, JSON.stringify(updatedExpenses));

    return newExpense;
  },

  updateExpense: async (expenseId: string, updates: Partial<Expense>): Promise<Expense> => {
    const userId = updates.userId || '';
    const expenses = await expenseService.getExpenses(userId);
    const index = expenses.findIndex(e => e.id === expenseId);

    if (index === -1) throw new Error('Expense not found');

    const updatedExpense = { ...expenses[index], ...updates, updatedAt: new Date().toISOString() };
    expenses[index] = updatedExpense;

    await AsyncStorage.setItem(`expenses_${userId}`, JSON.stringify(expenses));
    return updatedExpense;
  },

  deleteExpense: async (expenseId: string, userId: string): Promise<void> => {
    const expenses = await expenseService.getExpenses(userId);
    const filteredExpenses = expenses.filter(e => e.id !== expenseId);
    await AsyncStorage.setItem(`expenses_${userId}`, JSON.stringify(filteredExpenses));
  },

  getBudgets: async (userId: string): Promise<Budget[]> => {
    const data = await AsyncStorage.getItem(`budgets_${userId}`);
    return data ? JSON.parse(data) : [];
  },

  addBudget: async (budget: Omit<Budget, 'id'>): Promise<Budget> => {
    const newBudget: Budget = {
      ...budget,
      id: Date.now().toString(),
    };

    const existingBudgets = await expenseService.getBudgets(budget.userId);
    const updatedBudgets = [...existingBudgets, newBudget];
    await AsyncStorage.setItem(`budgets_${budget.userId}`, JSON.stringify(updatedBudgets));

    return newBudget;
  },

  updateBudget: async (budgetId: string, updates: Partial<Budget>): Promise<Budget> => {
    const userId = updates.userId || '';
    const budgets = await expenseService.getBudgets(userId);
    const index = budgets.findIndex(b => b.id === budgetId);

    if (index === -1) throw new Error('Budget not found');

    const updatedBudget = { ...budgets[index], ...updates };
    budgets[index] = updatedBudget;

    await AsyncStorage.setItem(`budgets_${userId}`, JSON.stringify(budgets));
    return updatedBudget;
  },

  getSalarySettings: async (userId: string): Promise<SalarySettings | null> => {
    const data = await AsyncStorage.getItem(`salarySettings_${userId}`);
    return data ? JSON.parse(data) : null;
  },

  saveSalarySettings: async (settings: SalarySettings): Promise<SalarySettings> => {
    await AsyncStorage.setItem(`salarySettings_${settings.userId}`, JSON.stringify(settings));
    return settings;
  },
};

// Async thunks
export const fetchExpenses = createAsyncThunk(
  'expenses/fetchExpenses',
  async (userId: string) => {
    return await expenseService.getExpenses(userId);
  }
);

export const addExpense = createAsyncThunk(
  'expenses/addExpense',
  async (expense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => {
    return await expenseService.addExpense(expense);
  }
);

export const updateExpense = createAsyncThunk(
  'expenses/updateExpense',
  async ({ expenseId, updates }: { expenseId: string; updates: Partial<Expense> }) => {
    return await expenseService.updateExpense(expenseId, updates);
  }
);

export const deleteExpense = createAsyncThunk(
  'expenses/deleteExpense',
  async ({ expenseId, userId }: { expenseId: string; userId: string }) => {
    await expenseService.deleteExpense(expenseId, userId);
    return expenseId;
  }
);

export const fetchBudgets = createAsyncThunk(
  'expenses/fetchBudgets',
  async (userId: string) => {
    return await expenseService.getBudgets(userId);
  }
);

export const addBudget = createAsyncThunk(
  'expenses/addBudget',
  async (budget: Omit<Budget, 'id'>) => {
    return await expenseService.addBudget(budget);
  }
);

export const updateBudget = createAsyncThunk(
  'expenses/updateBudget',
  async ({ budgetId, updates }: { budgetId: string; updates: Partial<Budget> }) => {
    return await expenseService.updateBudget(budgetId, updates);
  }
);

export const fetchSalarySettings = createAsyncThunk(
  'expenses/fetchSalarySettings',
  async (userId: string) => {
    return await expenseService.getSalarySettings(userId);
  }
);

export const saveSalarySettings = createAsyncThunk(
  'expenses/saveSalarySettings',
  async (settings: SalarySettings) => {
    return await expenseService.saveSalarySettings(settings);
  }
);

const initialState: ExpenseState = {
  expenses: [],
  categories: expenseCategories,
  budgets: [],
  salarySettings: null,
  isLoading: false,
  error: null,
};

const expenseSlice = createSlice({
  name: 'expenses',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCategories: (state, action: PayloadAction<ExpenseCategory[]>) => {
      state.categories = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch expenses
      .addCase(fetchExpenses.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchExpenses.fulfilled, (state, action) => {
        state.isLoading = false;
        state.expenses = action.payload;
      })
      .addCase(fetchExpenses.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch expenses';
      })
      // Add expense
      .addCase(addExpense.fulfilled, (state, action) => {
        state.expenses.push(action.payload);
      })
      // Update expense
      .addCase(updateExpense.fulfilled, (state, action) => {
        const index = state.expenses.findIndex(e => e.id === action.payload.id);
        if (index !== -1) {
          state.expenses[index] = action.payload;
        }
      })
      // Delete expense
      .addCase(deleteExpense.fulfilled, (state, action) => {
        state.expenses = state.expenses.filter(e => e.id !== action.payload);
      })
      // Fetch budgets
      .addCase(fetchBudgets.fulfilled, (state, action) => {
        state.budgets = action.payload;
      })
      // Add budget
      .addCase(addBudget.fulfilled, (state, action) => {
        state.budgets.push(action.payload);
      })
      // Update budget
      .addCase(updateBudget.fulfilled, (state, action) => {
        const index = state.budgets.findIndex(b => b.id === action.payload.id);
        if (index !== -1) {
          state.budgets[index] = action.payload;
        }
      })
      // Fetch salary settings
      .addCase(fetchSalarySettings.fulfilled, (state, action) => {
        state.salarySettings = action.payload;
      })
      // Save salary settings
      .addCase(saveSalarySettings.fulfilled, (state, action) => {
        state.salarySettings = action.payload;
      });
  },
});

export const { clearError, setCategories } = expenseSlice.actions;
export default expenseSlice.reducer;
