import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { FitnessState, Workout, WaterIntake, WeightEntry, SleepEntry, StepsData } from '../../types';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock fitness service
const fitnessService = {
  getWorkouts: async (userId: string): Promise<Workout[]> => {
    const data = await AsyncStorage.getItem(`workouts_${userId}`);
    return data ? JSON.parse(data) : [];
  },

  addWorkout: async (workout: Omit<Workout, 'id' | 'createdAt'>): Promise<Workout> => {
    const newWorkout: Workout = {
      ...workout,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
    };

    const existingWorkouts = await fitnessService.getWorkouts(workout.userId);
    const updatedWorkouts = [...existingWorkouts, newWorkout];
    await AsyncStorage.setItem(`workouts_${workout.userId}`, JSON.stringify(updatedWorkouts));

    return newWorkout;
  },

  updateWorkout: async (workoutId: string, updates: Partial<Workout>): Promise<Workout> => {
    const userId = updates.userId || '';
    const workouts = await fitnessService.getWorkouts(userId);
    const index = workouts.findIndex(w => w.id === workoutId);

    if (index === -1) throw new Error('Workout not found');

    const updatedWorkout = { ...workouts[index], ...updates };
    workouts[index] = updatedWorkout;

    await AsyncStorage.setItem(`workouts_${userId}`, JSON.stringify(workouts));
    return updatedWorkout;
  },

  deleteWorkout: async (workoutId: string, userId: string): Promise<void> => {
    const workouts = await fitnessService.getWorkouts(userId);
    const filteredWorkouts = workouts.filter(w => w.id !== workoutId);
    await AsyncStorage.setItem(`workouts_${userId}`, JSON.stringify(filteredWorkouts));
  },

  getWaterIntakes: async (userId: string): Promise<WaterIntake[]> => {
    const data = await AsyncStorage.getItem(`water_${userId}`);
    return data ? JSON.parse(data) : [];
  },

  addWaterIntake: async (intake: Omit<WaterIntake, 'id'>): Promise<WaterIntake> => {
    const newIntake: WaterIntake = {
      ...intake,
      id: Date.now().toString(),
    };

    const existingIntakes = await fitnessService.getWaterIntakes(intake.userId);
    const updatedIntakes = [...existingIntakes, newIntake];
    await AsyncStorage.setItem(`water_${intake.userId}`, JSON.stringify(updatedIntakes));

    return newIntake;
  },

  getWeightEntries: async (userId: string): Promise<WeightEntry[]> => {
    const data = await AsyncStorage.getItem(`weight_${userId}`);
    return data ? JSON.parse(data) : [];
  },

  addWeightEntry: async (entry: Omit<WeightEntry, 'id'>): Promise<WeightEntry> => {
    const newEntry: WeightEntry = {
      ...entry,
      id: Date.now().toString(),
      bmi: entry.bmi || 0, // Calculate BMI if height is available
    };

    const existingEntries = await fitnessService.getWeightEntries(entry.userId);
    const updatedEntries = [...existingEntries, newEntry];
    await AsyncStorage.setItem(`weight_${entry.userId}`, JSON.stringify(updatedEntries));

    return newEntry;
  },

  getSleepEntries: async (userId: string): Promise<SleepEntry[]> => {
    const data = await AsyncStorage.getItem(`sleep_${userId}`);
    return data ? JSON.parse(data) : [];
  },

  addSleepEntry: async (entry: Omit<SleepEntry, 'id'>): Promise<SleepEntry> => {
    const newEntry: SleepEntry = {
      ...entry,
      id: Date.now().toString(),
    };

    const existingEntries = await fitnessService.getSleepEntries(entry.userId);
    const updatedEntries = [...existingEntries, newEntry];
    await AsyncStorage.setItem(`sleep_${entry.userId}`, JSON.stringify(updatedEntries));

    return newEntry;
  },

  getStepsData: async (userId: string): Promise<StepsData[]> => {
    const data = await AsyncStorage.getItem(`steps_${userId}`);
    return data ? JSON.parse(data) : [];
  },

  addStepsData: async (data: Omit<StepsData, 'id'>): Promise<StepsData> => {
    const newData: StepsData = {
      ...data,
      id: Date.now().toString(),
    };

    const existingData = await fitnessService.getStepsData(data.userId);
    const updatedData = [...existingData, newData];
    await AsyncStorage.setItem(`steps_${data.userId}`, JSON.stringify(updatedData));

    return newData;
  },
};

// Async thunks
export const fetchWorkouts = createAsyncThunk(
  'fitness/fetchWorkouts',
  async (userId: string) => {
    return await fitnessService.getWorkouts(userId);
  }
);

export const addWorkout = createAsyncThunk(
  'fitness/addWorkout',
  async (workout: Omit<Workout, 'id' | 'createdAt'>) => {
    return await fitnessService.addWorkout(workout);
  }
);

export const updateWorkout = createAsyncThunk(
  'fitness/updateWorkout',
  async ({ workoutId, updates }: { workoutId: string; updates: Partial<Workout> }) => {
    return await fitnessService.updateWorkout(workoutId, updates);
  }
);

export const deleteWorkout = createAsyncThunk(
  'fitness/deleteWorkout',
  async ({ workoutId, userId }: { workoutId: string; userId: string }) => {
    await fitnessService.deleteWorkout(workoutId, userId);
    return workoutId;
  }
);

export const fetchWaterIntakes = createAsyncThunk(
  'fitness/fetchWaterIntakes',
  async (userId: string) => {
    return await fitnessService.getWaterIntakes(userId);
  }
);

export const addWaterIntake = createAsyncThunk(
  'fitness/addWaterIntake',
  async (intake: Omit<WaterIntake, 'id'>) => {
    return await fitnessService.addWaterIntake(intake);
  }
);

export const fetchWeightEntries = createAsyncThunk(
  'fitness/fetchWeightEntries',
  async (userId: string) => {
    return await fitnessService.getWeightEntries(userId);
  }
);

export const addWeightEntry = createAsyncThunk(
  'fitness/addWeightEntry',
  async (entry: Omit<WeightEntry, 'id'>) => {
    return await fitnessService.addWeightEntry(entry);
  }
);

export const fetchSleepEntries = createAsyncThunk(
  'fitness/fetchSleepEntries',
  async (userId: string) => {
    return await fitnessService.getSleepEntries(userId);
  }
);

export const addSleepEntry = createAsyncThunk(
  'fitness/addSleepEntry',
  async (entry: Omit<SleepEntry, 'id'>) => {
    return await fitnessService.addSleepEntry(entry);
  }
);

export const fetchStepsData = createAsyncThunk(
  'fitness/fetchStepsData',
  async (userId: string) => {
    return await fitnessService.getStepsData(userId);
  }
);

export const addStepsData = createAsyncThunk(
  'fitness/addStepsData',
  async (data: Omit<StepsData, 'id'>) => {
    return await fitnessService.addStepsData(data);
  }
);

const initialState: FitnessState = {
  workouts: [],
  waterIntakes: [],
  weightEntries: [],
  sleepEntries: [],
  stepsData: [],
  isLoading: false,
  error: null,
};

const fitnessSlice = createSlice({
  name: 'fitness',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Workouts
      .addCase(fetchWorkouts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWorkouts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.workouts = action.payload;
      })
      .addCase(fetchWorkouts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch workouts';
      })
      .addCase(addWorkout.fulfilled, (state, action) => {
        state.workouts.push(action.payload);
      })
      .addCase(updateWorkout.fulfilled, (state, action) => {
        const index = state.workouts.findIndex(w => w.id === action.payload.id);
        if (index !== -1) {
          state.workouts[index] = action.payload;
        }
      })
      .addCase(deleteWorkout.fulfilled, (state, action) => {
        state.workouts = state.workouts.filter(w => w.id !== action.payload);
      })
      // Water intakes
      .addCase(fetchWaterIntakes.fulfilled, (state, action) => {
        state.waterIntakes = action.payload;
      })
      .addCase(addWaterIntake.fulfilled, (state, action) => {
        state.waterIntakes.push(action.payload);
      })
      // Weight entries
      .addCase(fetchWeightEntries.fulfilled, (state, action) => {
        state.weightEntries = action.payload;
      })
      .addCase(addWeightEntry.fulfilled, (state, action) => {
        state.weightEntries.push(action.payload);
      })
      // Sleep entries
      .addCase(fetchSleepEntries.fulfilled, (state, action) => {
        state.sleepEntries = action.payload;
      })
      .addCase(addSleepEntry.fulfilled, (state, action) => {
        state.sleepEntries.push(action.payload);
      })
      // Steps data
      .addCase(fetchStepsData.fulfilled, (state, action) => {
        state.stepsData = action.payload;
      })
      .addCase(addStepsData.fulfilled, (state, action) => {
        state.stepsData.push(action.payload);
      });
  },
});

export const { clearError } = fitnessSlice.actions;
export default fitnessSlice.reducer;
