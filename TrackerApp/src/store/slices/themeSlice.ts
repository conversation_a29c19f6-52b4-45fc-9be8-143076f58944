import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ThemeState } from '../../types';
import { lightTheme, darkTheme } from '../../constants/theme';
import AsyncStorage from '@react-native-async-storage/async-storage';

const initialState: ThemeState = {
  isDarkMode: false,
  theme: lightTheme,
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    toggleTheme: (state) => {
      state.isDarkMode = !state.isDarkMode;
      state.theme = state.isDarkMode ? darkTheme : lightTheme;
      AsyncStorage.setItem('isDarkMode', JSON.stringify(state.isDarkMode));
    },
    setTheme: (state, action: PayloadAction<boolean>) => {
      state.isDarkMode = action.payload;
      state.theme = action.payload ? darkTheme : lightTheme;
      AsyncStorage.setItem('isDarkMode', JSON.stringify(action.payload));
    },
    loadTheme: (state, action: PayloadAction<boolean>) => {
      state.isDarkMode = action.payload;
      state.theme = action.payload ? darkTheme : lightTheme;
    },
  },
});

export const { toggleTheme, setTheme, loadTheme } = themeSlice.actions;
export default themeSlice.reducer;
