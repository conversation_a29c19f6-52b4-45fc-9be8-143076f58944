import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

const ErrorContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
`;

const ErrorTitle = styled(Text)`
  font-size: 24px;
  font-weight: bold;
  color: #dc3545;
  margin-bottom: 16px;
  text-align: center;
`;

const ErrorMessage = styled(Text)`
  font-size: 16px;
  color: #6c757d;
  text-align: center;
  margin-bottom: 24px;
  line-height: 24px;
`;

const RetryButton = styled(TouchableOpacity)`
  background-color: #007bff;
  padding: 12px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
`;

const RetryButtonText = styled(Text)`
  color: white;
  font-size: 16px;
  font-weight: 600;
`;

const DetailsButton = styled(TouchableOpacity)`
  padding: 8px 16px;
  border: 1px solid #6c757d;
  border-radius: 6px;
`;

const DetailsButtonText = styled(Text)`
  color: #6c757d;
  font-size: 14px;
`;

const ErrorDetails = styled(Text)`
  font-size: 12px;
  color: #6c757d;
  margin-top: 16px;
  padding: 16px;
  background-color: #f1f3f4;
  border-radius: 8px;
  font-family: monospace;
`;

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to console with safe error handling
    console.error('ErrorBoundary caught an error:', {
      message: error?.message || 'Unknown error',
      stack: error?.stack || 'No stack trace available',
      componentStack: errorInfo?.componentStack || 'No component stack available'
    });

    // Update state with error info
    this.setState({
      error,
      errorInfo
    });

    // You can also log the error to an error reporting service here
    // Example: logErrorToService(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ErrorContainer>
          <ErrorTitle>Oops! Something went wrong</ErrorTitle>
          <ErrorMessage>
            We're sorry, but something unexpected happened. Please try refreshing the app.
          </ErrorMessage>
          
          <RetryButton onPress={this.handleRetry}>
            <RetryButtonText>Try Again</RetryButtonText>
          </RetryButton>

          {__DEV__ && this.state.error && (
            <>
              <DetailsButton onPress={() => console.log('Full error details:', this.state)}>
                <DetailsButtonText>View Details (Dev Mode)</DetailsButtonText>
              </DetailsButton>
              
              <ErrorDetails>
                {this.state.error.message}
                {'\n\n'}
                {this.state.error.stack?.substring(0, 500)}
                {this.state.error.stack && this.state.error.stack.length > 500 ? '...' : ''}
              </ErrorDetails>
            </>
          )}
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
