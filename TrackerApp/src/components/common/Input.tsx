import React, { useState } from 'react';
import { TextInput, View, Text, TouchableOpacity, TextInputProps } from 'react-native';
import styled from 'styled-components/native';
import { useAppSelector } from '../../store';
import Icon from 'react-native-vector-icons/Ionicons';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  leftIcon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  containerStyle?: any;
}

const Container = styled(View)<{ theme: any }>`
  margin-bottom: ${props => props.theme.spacing.md}px;
`;

const Label = styled(Text)<{ theme: any }>`
  font-size: ${props => props.theme.typography.caption.fontSize}px;
  font-weight: ${props => props.theme.typography.h3.fontWeight};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.sm}px;
`;

const InputContainer = styled(View)<{ theme: any; hasError: boolean; isFocused: boolean }>`
  flex-direction: row;
  align-items: center;
  background-color: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.md}px;
  border-width: 1px;
  border-color: ${props => {
    if (props.hasError) return props.theme.colors.error;
    if (props.isFocused) return props.theme.colors.primary;
    return props.theme.colors.border;
  }};
  padding: ${props => props.theme.spacing.md}px;
`;

const StyledTextInput = styled(TextInput)<{ theme: any }>`
  flex: 1;
  font-size: ${props => props.theme.typography.body.fontSize}px;
  color: ${props => props.theme.colors.text};
  padding: 0;
`;

const IconContainer = styled(TouchableOpacity)<{ theme: any }>`
  margin-horizontal: ${props => props.theme.spacing.sm}px;
`;

const ErrorText = styled(Text)<{ theme: any }>`
  font-size: ${props => props.theme.typography.caption.fontSize}px;
  color: ${props => props.theme.colors.error};
  margin-top: ${props => props.theme.spacing.sm}px;
`;

export const Input: React.FC<InputProps> = ({
  label,
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  ...props
}) => {
  const theme = useAppSelector(state => state.theme.theme);
  const [isFocused, setIsFocused] = useState(false);

  return (
    <Container theme={theme} style={containerStyle}>
      {label && <Label theme={theme}>{label}</Label>}
      <InputContainer theme={theme} hasError={!!error} isFocused={isFocused}>
        {leftIcon && (
          <IconContainer theme={theme}>
            <Icon name={leftIcon} size={20} color={theme.colors.textSecondary} />
          </IconContainer>
        )}
        <StyledTextInput
          theme={theme}
          placeholderTextColor={theme.colors.textSecondary}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
        {rightIcon && (
          <IconContainer theme={theme} onPress={onRightIconPress}>
            <Icon name={rightIcon} size={20} color={theme.colors.textSecondary} />
          </IconContainer>
        )}
      </InputContainer>
      {error && <ErrorText theme={theme}>{error}</ErrorText>}
    </Container>
  );
};
