import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, ViewStyle, TextStyle } from 'react-native';
import styled from 'styled-components/native';
import { useAppSelector } from '../../store';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const StyledButton = styled(TouchableOpacity)<{
  variant: string;
  size: string;
  disabled: boolean;
  theme: any;
}>`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: ${props => props.theme.borderRadius.md}px;
  padding: ${props => {
    switch (props.size) {
      case 'small':
        return `${props.theme.spacing.sm}px ${props.theme.spacing.md}px`;
      case 'large':
        return `${props.theme.spacing.lg}px ${props.theme.spacing.xl}px`;
      default:
        return `${props.theme.spacing.md}px ${props.theme.spacing.lg}px`;
    }
  }};
  background-color: ${props => {
    if (props.disabled) return props.theme.colors.border;
    switch (props.variant) {
      case 'secondary':
        return props.theme.colors.secondary;
      case 'outline':
        return 'transparent';
      case 'ghost':
        return 'transparent';
      default:
        return props.theme.colors.primary;
    }
  }};
  border-width: ${props => (props.variant === 'outline' ? '1px' : '0px')};
  border-color: ${props => props.theme.colors.primary};
  opacity: ${props => (props.disabled ? 0.6 : 1)};
`;

const ButtonText = styled(Text)<{
  variant: string;
  size: string;
  theme: any;
}>`
  color: ${props => {
    switch (props.variant) {
      case 'outline':
      case 'ghost':
        return props.theme.colors.primary;
      default:
        return '#FFFFFF';
    }
  }};
  font-size: ${props => {
    switch (props.size) {
      case 'small':
        return props.theme.typography.caption.fontSize;
      case 'large':
        return props.theme.typography.h3.fontSize;
      default:
        return props.theme.typography.body.fontSize;
    }
  }}px;
  font-weight: ${props => props.theme.typography.h3.fontWeight};
  margin-left: ${props => props.theme.spacing.sm}px;
`;

const IconContainer = styled.View`
  margin-right: ${props => props.theme.spacing.sm}px;
`;

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  style,
  textStyle,
}) => {
  const theme = useAppSelector(state => state.theme.theme);

  const handlePress = () => {
    if (!disabled && !loading) {
      onPress();
    }
  };

  return (
    <StyledButton
      variant={variant}
      size={size}
      disabled={disabled || loading}
      theme={theme}
      onPress={handlePress}
      style={style}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'ghost' ? theme.colors.primary : '#FFFFFF'}
        />
      ) : (
        <>
          {icon && <IconContainer theme={theme}>{icon}</IconContainer>}
          <ButtonText
            variant={variant}
            size={size}
            theme={theme}
            style={textStyle}
          >
            {title}
          </ButtonText>
        </>
      )}
    </StyledButton>
  );
};
