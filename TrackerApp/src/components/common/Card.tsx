import React from 'react';
import { View, ViewStyle } from 'react-native';
import styled from 'styled-components/native';
import { useAppSelector } from '../../store';
import { shadows } from '../../constants/theme';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: 'none' | 'small' | 'medium' | 'large';
  shadow?: 'none' | 'small' | 'medium' | 'large';
  onPress?: () => void;
}

const StyledCard = styled(View)<{
  theme: any;
  padding: string;
  shadow: string;
  pressable: boolean;
}>`
  background-color: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg}px;
  border-width: 1px;
  border-color: ${props => props.theme.colors.border};
  padding: ${props => {
    switch (props.padding) {
      case 'none':
        return '0px';
      case 'small':
        return `${props.theme.spacing.sm}px`;
      case 'large':
        return `${props.theme.spacing.xl}px`;
      default:
        return `${props.theme.spacing.lg}px`;
    }
  }};
  ${props => {
    switch (props.shadow) {
      case 'small':
        return `
          shadow-color: ${shadows.small.shadowColor};
          shadow-offset: ${shadows.small.shadowOffset.width}px ${shadows.small.shadowOffset.height}px;
          shadow-opacity: ${shadows.small.shadowOpacity};
          shadow-radius: ${shadows.small.shadowRadius}px;
          elevation: ${shadows.small.elevation};
        `;
      case 'medium':
        return `
          shadow-color: ${shadows.medium.shadowColor};
          shadow-offset: ${shadows.medium.shadowOffset.width}px ${shadows.medium.shadowOffset.height}px;
          shadow-opacity: ${shadows.medium.shadowOpacity};
          shadow-radius: ${shadows.medium.shadowRadius}px;
          elevation: ${shadows.medium.elevation};
        `;
      case 'large':
        return `
          shadow-color: ${shadows.large.shadowColor};
          shadow-offset: ${shadows.large.shadowOffset.width}px ${shadows.large.shadowOffset.height}px;
          shadow-opacity: ${shadows.large.shadowOpacity};
          shadow-radius: ${shadows.large.shadowRadius}px;
          elevation: ${shadows.large.elevation};
        `;
      default:
        return '';
    }
  }}
`;

const PressableCard = styled.TouchableOpacity<{
  theme: any;
  padding: string;
  shadow: string;
}>`
  background-color: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg}px;
  border-width: 1px;
  border-color: ${props => props.theme.colors.border};
  padding: ${props => {
    switch (props.padding) {
      case 'none':
        return '0px';
      case 'small':
        return `${props.theme.spacing.sm}px`;
      case 'large':
        return `${props.theme.spacing.xl}px`;
      default:
        return `${props.theme.spacing.lg}px`;
    }
  }};
  ${props => {
    switch (props.shadow) {
      case 'small':
        return `
          shadow-color: ${shadows.small.shadowColor};
          shadow-offset: ${shadows.small.shadowOffset.width}px ${shadows.small.shadowOffset.height}px;
          shadow-opacity: ${shadows.small.shadowOpacity};
          shadow-radius: ${shadows.small.shadowRadius}px;
          elevation: ${shadows.small.elevation};
        `;
      case 'medium':
        return `
          shadow-color: ${shadows.medium.shadowColor};
          shadow-offset: ${shadows.medium.shadowOffset.width}px ${shadows.medium.shadowOffset.height}px;
          shadow-opacity: ${shadows.medium.shadowOpacity};
          shadow-radius: ${shadows.medium.shadowRadius}px;
          elevation: ${shadows.medium.elevation};
        `;
      case 'large':
        return `
          shadow-color: ${shadows.large.shadowColor};
          shadow-offset: ${shadows.large.shadowOffset.width}px ${shadows.large.shadowOffset.height}px;
          shadow-opacity: ${shadows.large.shadowOpacity};
          shadow-radius: ${shadows.large.shadowRadius}px;
          elevation: ${shadows.large.elevation};
        `;
      default:
        return '';
    }
  }}
`;

export const Card: React.FC<CardProps> = ({
  children,
  style,
  padding = 'medium',
  shadow = 'small',
  onPress,
}) => {
  const theme = useAppSelector(state => state.theme.theme);

  if (onPress) {
    return (
      <PressableCard
        theme={theme}
        padding={padding}
        shadow={shadow}
        style={style}
        onPress={onPress}
        activeOpacity={0.7}
      >
        {children}
      </PressableCard>
    );
  }

  return (
    <StyledCard
      theme={theme}
      padding={padding}
      shadow={shadow}
      pressable={false}
      style={style}
    >
      {children}
    </StyledCard>
  );
};
