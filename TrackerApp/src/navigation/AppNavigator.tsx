import React, { useEffect } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { useAppSelector, useAppDispatch } from "../store";
import { loadUser } from "../store/slices/authSlice";
import { loadTheme, toggleTheme } from "../store/slices/themeSlice";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Icon from "react-native-vector-icons/Ionicons";
import { TouchableOpacity } from "react-native";
import { safeConsoleLog, safeConsoleError } from "../utils/errorUtils";

// Import screens (we'll create these next)
import AuthScreen from "../screens/auth/AuthScreen";
import DashboardScreen from "../screens/DashboardScreen";
import ExpenseListScreen from "../screens/expense/ExpenseListScreen";
import FitnessOverviewScreen from "../screens/fitness/FitnessOverviewScreen";
import AnalyticsScreen from "../screens/AnalyticsScreen";
import ProfileScreen from "../screens/profile/ProfileScreen";

import { RootStackParamList, MainTabParamList } from "../types";

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator = () => {
  const dispatch = useAppDispatch();
  const theme = useAppSelector((state) => state.theme.theme);
  const { isDarkMode } = useAppSelector((state) => state.theme);

  // Safety check for theme
  if (!theme || !theme.fonts) {
    return null;
  }

  const handleToggleTheme = () => {
    dispatch(toggleTheme());
  };

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case "Dashboard":
              iconName = focused ? "home" : "home-outline";
              break;
            case "Expenses":
              iconName = focused ? "wallet" : "wallet-outline";
              break;
            case "Fitness":
              iconName = focused ? "fitness" : "fitness-outline";
              break;
            case "Analytics":
              iconName = focused ? "analytics" : "analytics-outline";
              break;
            default:
              iconName = "circle";
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerStyle: {
          backgroundColor: theme.colors.surface,
          borderBottomColor: theme.colors.border,
        },
        headerTintColor: theme.colors.text,
        headerTitleStyle: {
          fontWeight: "600",
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{ title: "Dashboard" }}
      />
      <Tab.Screen
        name="Expenses"
        component={ExpenseListScreen}
        options={{
          title: "Expenses",
          headerRight: () => (
            <TouchableOpacity
              onPress={handleToggleTheme}
              style={{ marginRight: 15 }}
            >
              <Icon
                name={isDarkMode ? "sunny" : "moon"}
                size={24}
                color={theme.colors.text}
              />
            </TouchableOpacity>
          ),
        }}
      />
      <Tab.Screen
        name="Fitness"
        component={FitnessOverviewScreen}
        options={{ title: "Fitness" }}
      />
      <Tab.Screen
        name="Analytics"
        component={AnalyticsScreen}
        options={{ title: "Analytics" }}
      />
    </Tab.Navigator>
  );
};

export const AppNavigator = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, isLoading } = useAppSelector((state) => state.auth);
  const theme = useAppSelector((state) => state.theme.theme);

  // Safety check for theme
  if (!theme || !theme.fonts) {
    return null;
  }

  useEffect(() => {
    const initializeApp = async () => {
      // Clear storage for development (remove this in production)
      try {
        await AsyncStorage.clear();
        safeConsoleLog("Storage cleared for development");
      } catch (error) {
        safeConsoleError("Error clearing storage:", error);
      }

      // Load theme preference
      try {
        const savedTheme = await AsyncStorage.getItem("isDarkMode");
        if (savedTheme !== null) {
          dispatch(loadTheme(JSON.parse(savedTheme)));
        }
      } catch (error) {
        safeConsoleError("Error loading theme:", error);
      }

      // Load user
      dispatch(loadUser());
    };

    initializeApp();
  }, [dispatch]);

  if (isLoading) {
    // You can replace this with a proper loading screen
    return null;
  }

  return (
    <NavigationContainer
      theme={{
        dark: false,
        colors: {
          primary: theme.colors.primary,
          background: theme.colors.background,
          card: theme.colors.surface,
          text: theme.colors.text,
          border: theme.colors.border,
          notification: theme.colors.error,
        },
        fonts: {
          regular: {
            fontFamily: "System",
            fontWeight: "normal",
          },
          medium: {
            fontFamily: "System",
            fontWeight: "500",
          },
          bold: {
            fontFamily: "System",
            fontWeight: "bold",
          },
          heavy: {
            fontFamily: "System",
            fontWeight: "900",
          },
        },
      }}
    >
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: theme.colors.surface,
            borderBottomColor: theme.colors.border,
          },
          headerTintColor: theme.colors.text,
          headerTitleStyle: {
            fontWeight: "600",
          },
        }}
      >
        {isAuthenticated ? (
          <>
            <Stack.Screen
              name="Main"
              component={MainTabNavigator}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="Profile"
              component={ProfileScreen}
              options={{ title: "Profile" }}
            />
          </>
        ) : (
          <Stack.Screen
            name="Auth"
            component={AuthScreen}
            options={{ headerShown: false }}
          />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
