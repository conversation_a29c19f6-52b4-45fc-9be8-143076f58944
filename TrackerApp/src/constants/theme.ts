import { Theme } from '../types';

export const lightTheme: Theme = {
  colors: {
    primary: '#6366F1',
    secondary: '#EC4899',
    background: '#FFFFFF',
    surface: '#F8FAFC',
    text: '#1E293B',
    textSecondary: '#64748B',
    border: '#E2E8F0',
    error: '#EF4444',
    success: '#10B981',
    warning: '#F59E0B',
    info: '#3B82F6',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  typography: {
    h1: {
      fontSize: 32,
      fontWeight: 'bold',
    },
    h2: {
      fontSize: 24,
      fontWeight: 'bold',
    },
    h3: {
      fontSize: 20,
      fontWeight: '600',
    },
    body: {
      fontSize: 16,
      fontWeight: 'normal',
    },
    caption: {
      fontSize: 14,
      fontWeight: 'normal',
    },
  },
  fonts: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 16,
    xl: 24,
  },
};

export const darkTheme: Theme = {
  colors: {
    primary: '#818CF8',
    secondary: '#F472B6',
    background: '#0F172A',
    surface: '#1E293B',
    text: '#F1F5F9',
    textSecondary: '#94A3B8',
    border: '#334155',
    error: '#F87171',
    success: '#34D399',
    warning: '#FBBF24',
    info: '#60A5FA',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  typography: {
    h1: {
      fontSize: 32,
      fontWeight: 'bold',
    },
    h2: {
      fontSize: 24,
      fontWeight: 'bold',
    },
    h3: {
      fontSize: 20,
      fontWeight: '600',
    },
    body: {
      fontSize: 16,
      fontWeight: 'normal',
    },
    caption: {
      fontSize: 14,
      fontWeight: 'normal',
    },
  },
  fonts: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 16,
    xl: 24,
  },
};

export const expenseCategories = [
  { id: '1', name: 'Food & Dining', icon: 'restaurant', color: '#EF4444' },
  { id: '2', name: 'Transportation', icon: 'car', color: '#3B82F6' },
  { id: '3', name: 'Shopping', icon: 'bag', color: '#EC4899' },
  { id: '4', name: 'Entertainment', icon: 'game-controller', color: '#8B5CF6' },
  { id: '5', name: 'Health & Fitness', icon: 'fitness', color: '#10B981' },
  { id: '6', name: 'Bills & Utilities', icon: 'receipt', color: '#F59E0B' },
  { id: '7', name: 'Education', icon: 'school', color: '#06B6D4' },
  { id: '8', name: 'Travel', icon: 'airplane', color: '#84CC16' },
  { id: '9', name: 'Personal Care', icon: 'person', color: '#F97316' },
  { id: '10', name: 'Other', icon: 'ellipsis-horizontal', color: '#6B7280' },
];

export const fitnessGoals = [
  'Lose Weight',
  'Gain Muscle',
  'Maintain Weight',
  'Improve Endurance',
  'General Fitness',
  'Strength Training',
  'Flexibility',
  'Sports Performance',
];

export const workoutTypes = [
  { name: 'Cardio', icon: 'heart', color: '#EF4444' },
  { name: 'Strength', icon: 'barbell', color: '#3B82F6' },
  { name: 'Yoga', icon: 'leaf', color: '#10B981' },
  { name: 'Running', icon: 'run', color: '#F59E0B' },
  { name: 'Cycling', icon: 'bicycle', color: '#8B5CF6' },
  { name: 'Swimming', icon: 'water', color: '#06B6D4' },
  { name: 'Walking', icon: 'walk', color: '#84CC16' },
  { name: 'Other', icon: 'fitness', color: '#6B7280' },
];

export const chartColors = [
  '#6366F1',
  '#EC4899',
  '#10B981',
  '#F59E0B',
  '#8B5CF6',
  '#EF4444',
  '#06B6D4',
  '#84CC16',
  '#F97316',
  '#6B7280',
];

export const animations = {
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  slideInUp: {
    from: { transform: [{ translateY: 50 }], opacity: 0 },
    to: { transform: [{ translateY: 0 }], opacity: 1 },
  },
  slideInDown: {
    from: { transform: [{ translateY: -50 }], opacity: 0 },
    to: { transform: [{ translateY: 0 }], opacity: 1 },
  },
  slideInLeft: {
    from: { transform: [{ translateX: -50 }], opacity: 0 },
    to: { transform: [{ translateX: 0 }], opacity: 1 },
  },
  slideInRight: {
    from: { transform: [{ translateX: 50 }], opacity: 0 },
    to: { transform: [{ translateX: 0 }], opacity: 1 },
  },
  scale: {
    from: { transform: [{ scale: 0.8 }], opacity: 0 },
    to: { transform: [{ scale: 1 }], opacity: 1 },
  },
};

export const shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};
