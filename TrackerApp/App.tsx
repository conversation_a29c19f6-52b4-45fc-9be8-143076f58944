import React, { useEffect } from "react";
import { StatusBar } from "expo-status-bar";
import { Provider } from "react-redux";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { store } from "./src/store";
import { AppNavigator } from "./src/navigation/AppNavigator";
import ErrorBoundary from "./src/components/common/ErrorBoundary";
import { safeConsoleError } from "./src/utils/errorUtils";
import { Platform } from "react-native";

export default function App() {
  useEffect(() => {
    if (Platform.OS === "web") {
      // Global error handler for unhandled promise rejections
      const handleUnhandledRejection = (event: any) => {
        safeConsoleError("Unhandled promise rejection:", event.reason);
        event.preventDefault();
      };

      // Global error handler for uncaught errors
      const handleError = (event: any) => {
        safeConsoleError("Uncaught error:", event.error);
        event.preventDefault();
      };

      window.addEventListener("unhandledrejection", handleUnhandledRejection);
      window.addEventListener("error", handleError);

      // Cleanup function
      return () => {
        window.removeEventListener(
          "unhandledrejection",
          handleUnhandledRejection
        );
        window.removeEventListener("error", handleError);
      };
    }
  }, []);

  return (
    <ErrorBoundary>
      <Provider store={store}>
        <SafeAreaProvider>
          <ErrorBoundary>
            <AppNavigator />
          </ErrorBoundary>
          <StatusBar style="auto" />
        </SafeAreaProvider>
      </Provider>
    </ErrorBoundary>
  );
}
