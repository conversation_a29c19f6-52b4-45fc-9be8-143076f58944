{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@reduxjs/toolkit": "^2.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "expo": "~53.0.9", "expo-constants": "^17.1.6", "expo-device": "^7.1.4", "expo-font": "^13.3.1", "expo-linear-gradient": "^14.1.4", "expo-local-authentication": "^16.0.4", "expo-notifications": "^0.31.2", "expo-status-bar": "~2.2.3", "lottie-react-native": "^7.2.2", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.4", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-redux": "^9.2.0", "styled-components": "^6.1.18", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "victory-native": "^41.17.1", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "@types/styled-components": "^5.1.34", "typescript": "~5.8.3"}, "private": true}